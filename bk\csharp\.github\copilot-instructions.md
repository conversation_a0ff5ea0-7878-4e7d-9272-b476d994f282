# Copilot Instructions for Artemis Expenses C# Project

This project is a C# API for managing Artemis expenses, inspired by Mule flows in `interface.xml`. It will support endpoints for account, product, company, and transaction management. Configuration and certificates for different environments are referenced from the `config` and `certificates` folders.

## Setup Steps
- Scaffold a new C# Web API project in the `csharp` folder.
- Add environment-based configuration referencing the provided config files.
- Integrate certificate-based security using the provided keystore/truststore files.
- Implement API endpoints for account, product, company, and transaction, following the Mule flow logic.
- Ensure README.md is up to date with setup and usage instructions.

## Progress Checklist
- [ ] Project scaffolded
- [ ] Environment config integrated
- [ ] Certificate security setup
- [ ] API endpoints implemented
- [ ] README.md updated

Update this file as you complete each step.
