<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:validation="http://www.mulesoft.org/schema/mule/validation" xmlns:os="http://www.mulesoft.org/schema/mule/os"
	xmlns:aggregators="http://www.mulesoft.org/schema/mule/aggregators"
	xmlns:vm="http://www.mulesoft.org/schema/mule/vm" xmlns:batch="http://www.mulesoft.org/schema/mule/batch" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/batch http://www.mulesoft.org/schema/mule/batch/current/mule-batch.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/aggregators http://www.mulesoft.org/schema/mule/aggregators/current/mule-aggregators.xsd
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd">
	<flow name="company_transactions_get" doc:id="a2a89628-4c53-4216-9866-4410976b9a98" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="71f399bb-5348-4e1f-9f47-6ea3a267c82b" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<ee:transform doc:name="Request" doc:id="7c2e38de-7275-4b18-ab9b-b4649c70d575">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="filters" ><![CDATA[output text/plain
---
if(isEmpty(attributes.uriParams.transactionID))
"WHERE CounterpartyID = $(attributes.uriParams.companyID)
AND ConfirmID IS NOT NULL"
else "WHERE CounterpartyID = $(attributes.uriParams.companyID) 
AND ConfirmID = $(attributes.uriParams.transactionID)"]]></ee:set-variable>
				<ee:set-variable variableName="parameters" ><![CDATA[%dw 2.0
var companyID = attributes.uriParams.companyID
var transactionID = if(isEmpty(attributes.uriParams.transactionID)) "" else attributes.uriParams.transactionID
var products = if(isEmpty(attributes.queryParams.products)) '' else attributes.queryParams.products
var startDate = if(isEmpty(attributes.queryParams.startDate)) '' else attributes.queryParams.startDate
var endDate = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.endDate
var reportingYears = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.reportingYears
var params = "@CompanyID=" ++ companyID as String ++
";@Products=" ++ products as String ++
";@StartDate=" ++ startDate as String ++
";@EndDate=" ++ endDate as String ++
";@ReportingYears=" ++ reportingYears as String ++
";@PageSize=" ++ attributes.queryParams.pageSize as String ++
";@PageNumber=" ++ attributes.queryParams.pageNumber as String
output text/plain
---
if(isEmpty(transactionID)) params else params ++ ";@TransactionID=" ++ transactionID]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<!-- <http:request method="GET" doc:name="ERP Artemis API Stored Procedure" doc:id="9abd22c8-5a3b-4242-808c-ed9f2b2ef961" config-ref="https_request_artemisapi" path="/artemis/stored-procedure" sendBodyMode="NEVER">
					<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	database: Mule::p('secure::cp.https.request.query.database'),
	schema: Mule::p('secure::cp.https.request.query.schema'),
	objectType: Mule::p('secure::cp.https.request.query.objectType.sp'),
	objectName: Mule::p('secure::cp.https.request.query.objectName.companies.transactions'),
	parameters: vars.parameters
}]]]></http:query-params>
				</http:request> -->
		<try doc:name="Try Retrieving Company Transactions" doc:id="efefb994-475a-4250-bb06-87eb93bc3faa">
			<http:request method="GET" doc:name="Calling Artemis SYS API" doc:id="0ac15334-ff80-4898-81e5-45381cd8504b" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema'),
					 	     
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.companies.transactions'),
	parameters: vars.parameters
}]]]></http:query-params>
			</http:request>
		</try> 
		<ee:transform doc:name="payload" doc:id="9e6d401f-46e1-4296-9d5e-0869a7bbab49" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table map() -> {
		agreement_id: $.agreement_id,
		po_number: $.po_number,
		order_date: if ( isEmpty($.order_date) ) "" else $.order_date as DateTime as String {
			format: "uuuu-MM-dd HH:mm:ss.SSS+00:00"
		},
		sales_team_contact_name: $.sales_team_contact_name,
		sales_team_contact_email: $.sales_team_contact_email,
		payment_status: $.payment_status,
		delivery_status: $.delivery_status,
		location: $.location,
		reporting_years: $.reporting_years,
		// reporting_years: ((item.reporting_years splitBy(",")) map ($ as String) default []),
		total_cost: if($.total_cost != null and $.total_cost != 0) ($.total_cost as String {format: ".00"
		}) else "",
		currency: $.currency 
	},
	paging: payload.response.Table1[0]
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="e0ff96c0-5d68-4738-9c0e-a1ae97647388" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</flow>
	<flow name="company_transactions_commodities_get" doc:id="480a22f5-989d-42f2-a937-165b56f0473f" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="06306fed-6b8b-4e50-9564-d125db492257" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_commodities_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<ee:transform doc:name="Request" doc:id="4d124f94-4d98-46e7-83e1-348e28442409" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="parameters" ><![CDATA[%dw 2.0
var companyID = attributes.uriParams.companyID
var transactionID = if(isEmpty(attributes.uriParams.transactionID)) "" else attributes.uriParams.transactionID
var products = if(isEmpty(attributes.queryParams.products)) '' else attributes.queryParams.products
var startDate = if(isEmpty(attributes.queryParams.startDate)) '' else attributes.queryParams.startDate
var endDate = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.endDate
var reportingYears = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.reportingYears
var params = "@CompanyID=" ++ companyID as String ++
";@Products=" ++ products as String ++
";@StartDate=" ++ startDate as String ++
";@EndDate=" ++ endDate as String ++
";@ReportingYears=" ++ reportingYears as String ++
";@PageSize=" ++ attributes.queryParams.pageSize as String ++
";@PageNumber=" ++ attributes.queryParams.pageNumber as String
output text/plain
---
if(isEmpty(transactionID)) params else params ++ ";@TransactionID=" ++ transactionID]]></ee:set-variable>
				<ee:set-variable variableName="filters" ><![CDATA[output text/plain
---
if(isEmpty(attributes.uriParams.transactionID))
"WHERE CounterpartyID = $(attributes.uriParams.companyID)
AND ConfirmID IS NOT NULL"
else "WHERE CounterpartyID = $(attributes.uriParams.companyID) 
AND ConfirmID = $(attributes.uriParams.transactionID)"]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<http:request method="GET" doc:name="Call Artemis SYS api" doc:id="3b184b8d-2d46-4eb0-a65c-eda51096ebc5" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.companies.transactions.commodities'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		<choice doc:name="Choice" doc:id="579edb80-e712-4800-9c60-df48d419ef0c" >
			<when expression='#[sizeOf(payload.response.Table default []) &gt; 1]'>
				<ee:transform doc:name="payload" doc:id="bfc72372-cb47-4aa3-bd3d-6eecc0eb44dd">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table,
	paging: payload.response.Table1[0]
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
			</when>
			<otherwise>
				<ee:transform doc:name="Transform Message" doc:id="3aef42cd-40e3-4fe9-8686-8dc45d01cdd8">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: []
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="1350193e-ae7c-411a-acd2-baf2e518315d" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />


	</flow>
	<flow name="company_transactions_products_get" doc:id="b4862aae-7611-4a85-beff-c112fd6486c1" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="ff64ace5-38a2-4bf8-9489-288e07a4f95b" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_products_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<ee:transform doc:name="Request" doc:id="43d39333-49c4-40d1-b225-3a6f00e5c2b9" >
			<ee:message />
			<ee:variables >
				<ee:set-variable variableName="parameters" ><![CDATA[%dw 2.0
var companyID = attributes.uriParams.companyID
var transactionID = if(isEmpty(attributes.uriParams.transactionID)) "" else attributes.uriParams.transactionID
var products = if(isEmpty(attributes.queryParams.products)) '' else attributes.queryParams.products
var startDate = if(isEmpty(attributes.queryParams.startDate)) '' else attributes.queryParams.startDate
var endDate = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.endDate
var reportingYears = if(isEmpty(attributes.queryParams.endDate)) '' else attributes.queryParams.reportingYears
var params = "@CompanyID=" ++ companyID as String ++
";@Products=" ++ products as String ++
";@StartDate=" ++ startDate as String ++
";@EndDate=" ++ endDate as String ++
";@ReportingYears=" ++ reportingYears as String ++
";@PageSize=" ++ attributes.queryParams.pageSize as String ++
";@PageNumber=" ++ attributes.queryParams.pageNumber as String
output text/plain
---
if(isEmpty(transactionID)) params else params ++ ";@TransactionID=" ++ transactionID]]></ee:set-variable>
				<ee:set-variable variableName="filters" ><![CDATA[output text/plain
---
if(isEmpty(attributes.uriParams.transactionID))
"WHERE CounterpartyID = $(attributes.uriParams.companyID)
AND ConfirmID IS NOT NULL"
else "WHERE CounterpartyID = $(attributes.uriParams.companyID) 
AND ConfirmID = $(attributes.uriParams.transactionID)"]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<!-- <try doc:name="Try Executing Stored Procedure" doc:id="734ca3d9-b102-4ab4-b90a-8631162f9b27" >
			<http:request method="GET" doc:name="ERP Artemis API Stored Procedure" doc:id="f3df2251-acc1-41e9-8fcf-48ba44b8ad5b" config-ref="https_request_artemisapi" path="/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	database: Mule::p('secure::cp.https.request.query.database'),
	schema: Mule::p('secure::cp.https.request.query.cp.schema'),
	objectType: Mule::p('secure::cp.https.request.query.objectType.sp'),
	objectName: Mule::p('secure::cp.https.request.query.objectName.companies.transactions.products'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		</try> -->
		 <try doc:name="Try Retrieving Company Products" doc:id="7376f58e-1e46-44e2-8ffa-00bcb44f50a4">
			<http:request method="GET" doc:name="Calling Artemis SYS API" doc:id="1e14a290-9cbb-47f7-9f2b-157246c19ab0" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.companies.transactions.products'),
	parameters: vars.parameters
}]]]></http:query-params>
			</http:request>
			
		</try> 
		<ee:transform doc:name="payload" doc:id="8083e809-3765-4464-92a5-3defcae10450" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table,
	paging: payload.response.Table1[0]
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="a3d02794-651a-4597-8487-890a8245e74b" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-transactions\company_transactions_products_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />


	</flow>
</mule>
