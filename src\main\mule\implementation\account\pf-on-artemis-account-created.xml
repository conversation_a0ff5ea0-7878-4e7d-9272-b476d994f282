<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-artemis-account-created"
		doc:id="d41e3506-8ba5-4e5b-ba61-2025ce1f78e8" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="7da03372-e1d0-4eaa-9fe9-7f14cd3a12eb"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-artemis-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="f65f2f0d-2da3-4fcb-bdaf-e5b7bf296b82"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-artemis-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="a2a83dac-2fc1-4a1f-b393-06faba5394d8"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-artemis-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload&#10;}]' />
		<choice doc:name="Check enableSyncForArtemis" doc:id="9899cd21-d862-43b6-ae7a-0c470ddacd55" >
			<when expression="#[Mule::p('enableSyncForArtemis') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is enabled" doc:id="8873d520-1e4c-4949-9b11-2c7b7d1d0fb3" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForArtemis is enabled", &#10;	"FlowName" : "pf-on-artemis-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]'/>
				<set-variable value="#[output application/json --- []]" doc:name="vFinalResponse" doc:id="851c8694-6a90-4a12-a467-7f5ef36066ef" variableName="vFinalResponse" />
				<foreach doc:name="For Each Artemis record" doc:id="fdadabbb-7798-403b-a8fb-8e6d21b2f401" collection="#[payload.'account' default []]">
				
				<set-payload value='#[output application/json&#10;---&#10;payload mapObject(v, k, i) -&gt; {&#10;    ((k) match {&#10;        case "counterpartyID" -&gt; "counterpartyId"&#10;        else -&gt; (k)&#10;    }): (v)&#10;}]' doc:name="Set Payload" doc:id="76fbfda4-2fce-409c-80a8-3da6b2397f6f" />
				
				<try doc:name="Try" doc:id="745ef150-b8e9-4b2a-96c0-2f81f72b0203">
				<set-variable value='#[output application/json&#10;---&#10;(vars.vOrigCorrelationId default "") ++ if((vars.counter as Number) == 1) "" else ("_" ++ ((vars.counter as Number) - 1))]' doc:name="Update vCorrelationId" doc:id="1bdc94e4-59e9-426c-b1f0-505992e65ea7" variableName="vCorrelationId" />
				<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;headers&quot;: {&#10;		&quot;x-source&quot;: &quot;ARTEMIS&quot;,&#10;		&quot;x-transactionId&quot;: vars.vTransactionId,&#10;		&quot;x-msg-timestamp&quot;: (now() as LocalDateTime {format: &quot;yyyy-MM-dd'T'HH:mm:ss.000'Z'&quot;}),&#10;		&quot;correlationId&quot;: vars.vCorrelationId,&#10;		&quot;sourceId&quot;: &quot;ARTEMIS_EXP_API&quot;,&#10;		&quot;destinationId&quot;: &quot;TRANSACTION_DB_SYS_API&quot;,&#10;		&quot;content-type&quot;: &quot;application/json&quot;&#10;	}&#10;}]" doc:name="vRequestAttributes" doc:id="12b28ccb-ecd3-4448-918c-684a9adf0e29" variableName="vRequestAttributes" />
				<flow-ref doc:name="Flow Reference to sf-insert-record" doc:id="df3655e9-dc37-48f3-8c88-c145943e1526" name="sf-insert-record" />
				<error-handler>
					<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="f1c4651b-658e-42c6-9111-b94ed8bac087" type="ANY">
						<logger level="WARN" doc:name="LOG WARN: Errorneous record" doc:id="a197e791-45ea-4d33-b2c1-6dbb56691c61" message='#[output application/json&#10;---&#10;{&#10;	"message": "Error while inserting record into Database",&#10;	"error": error.description	&#10;}]' />
						<set-variable value="#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	&quot;counterpartyID&quot;: payload.'counterpartyId' default &quot;-1&quot;,&#10;	&quot;eid&quot;: vars.vInsertRefIdResponse.response.'ENTERPRISE_ID' default &quot;-1&quot;,&#10;	&quot;flag&quot;: false&#10;})]" doc:name="Update vFinalResponse" doc:id="11e6748e-e66a-4f8b-b61b-c1cc80c0ab79" variableName="vFinalResponse" />
					</on-error-continue>
				</error-handler>
			</try>
		</foreach>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="cdea1001-499d-45db-8106-992e30ef123e" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) 201 else 400,
  "transactionId": vars.vTransactionId,
  "status": if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) "SUCCESS" else "FAILURE",
  "response": vars.vFinalResponse
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
---
if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) 201 else 400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is disabled" doc:id="f166cb8f-9e48-4417-8578-bbd1af8ad33b" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForArtemis is disabled", &#10;	"FlowName" : "pf-on-artemis-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]'/>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="223bcdab-f563-4efa-a52c-d43c9e8260b0" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForArtemis is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="a52b3ed9-b7b2-4e3a-8257-50e155def17c"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-artemis-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="e4d957b2-3356-4c48-9b45-0b8404dbf81b"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-artemis-account-created",&#10;	"CorrelationID": vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="dd6b4009-4ace-448b-ba32-ab0875571b27"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-artemis-account-created",&#10;	"CorrelationID": vars.vOrigCorrelationId&#10;}]' />
	</flow>
	<sub-flow name="sf-insert-record"
		doc:id="340fae10-18d2-46a6-974c-252efa2bc3c9">
		<choice doc:name="Check doNotSync" doc:id="14a9e331-ca5a-460a-b1a3-96b3385d17e1" >
			<when expression="#[!(payload.'doNotSync' as Boolean default false)]">
				<http:request method="POST" doc:name="Insert into REF_ID" doc:id="5b66429b-8732-4cdc-9fd8-2c301d55b111" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]" target="vInsertRefIdResponse">
			<http:body><![CDATA[#[%dw 2.0
output application/json skipNullOn='everywhere'
---
{
   "refID":{
     "OBJECT_TYPE": "ACCOUNT",
     "ARTEMIS_ID": payload.'counterpartyId',
     "LAST_UPDATED_BY": "ARTEMIS_API"
  }
}]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
		</http:request>
				<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="5806f6fa-e994-44b3-8613-ce7a6b3ada15">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
output application/json
---
{
    "transaction": {
		"CORRELATION_ID": vars.vCorrelationId,
	    "OPERATION": "CREATE",
	    "SOURCE": "ARTEMIS",
	    "STATUS": "QUEUED",
	    "LAST_UPDATED_BY": "EXPERIENCE_API",
	    "ENTERPRISE_ID":  vars.vInsertRefIdResponse.response.'ENTERPRISE_ID', 
	    "PAYLOAD": write({"account": payload}, 'application/json') default null,
	    "PRIORITY":(payload.syncPriority default "0") as Number,
	    "OBJECT_TYPE": "ACCOUNT",
	    "QUERY_PARAMS": null,
	    "RETRY_COUNT": 0,
	    "ERROR_MSG": null,
	    "ERROR_TYPE": null
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAILS" doc:id="e73cb152-a3a7-4a19-9f34-21bac86d5f67" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionDetails.path')]" target="vInsertTransactionResponse">
			<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
		</http:request>
				<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="74755e78-0680-4ff6-98d1-8dde93cb5630" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')]" target="vSyncRecordResponse" >
					<http:body ><![CDATA[#[{}]]]></http:body>
					<http:headers ><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
				</http:request>
				<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="f17da519-5b6a-4f97-8559-9c3f0fc118be" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-on-artemis-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
				<set-variable value="#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	&quot;counterpartyID&quot;: payload.'counterpartyId' default &quot;-1&quot;,&#10;	&quot;eid&quot;: vars.vInsertRefIdResponse.response.'ENTERPRISE_ID',&#10;	&quot;flag&quot;: if(vars.vInsertTransactionResponse.'status' ~= &quot;SUCCESS&quot;) true else false&#10;})]" doc:name="Update vFinalResponse" doc:id="9c8a031c-254a-42a7-a452-6fa5c26e0b24" variableName="vFinalResponse" />
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: Discard sync" doc:id="3712ded5-7fa6-4daf-b5e9-6d7dee89fbe2" message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: &quot;Record with CounterpartyId &quot; ++ payload.'counterpartyId' ++ &quot; is ignored as doNotSync is true.&quot;,&#10;	&quot;FlowName&quot;: &quot;sf-insert-record&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId&#10;}]"/>
				<set-variable value='#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	"message": "Record will not be processed for syncing",&#10;	"description": "doNotSync is set to true",&#10;	"flag": false&#10;})]' doc:name="Update vFinalResponse" doc:id="7634cc4f-a5a7-4eea-880f-cb2e479af910" variableName="vFinalResponse" />
			</otherwise>
		</choice>
	</sub-flow>
</mule>
