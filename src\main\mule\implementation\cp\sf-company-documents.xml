<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:aggregators="http://www.mulesoft.org/schema/mule/aggregators" xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	xmlns:batch="http://www.mulesoft.org/schema/mule/batch"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/batch http://www.mulesoft.org/schema/mule/batch/current/mule-batch.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/aggregators http://www.mulesoft.org/schema/mule/aggregators/current/mule-aggregators.xsd">
	<flow name="company_documents_agreements_get" doc:id="d18a5b8b-83db-4d44-9546-61c164b726a4" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="54c1c2eb-d86e-4b22-88c0-a95ce0f99ffb" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_agreements_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		 <set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@DocumentType=Agreements" ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="01f266c3-bfeb-47b5-8dc0-d24e3044d9fb" variableName="parameters" />  
		 <try doc:name="Try Executing Stored Procedure" doc:id="699d0b2f-8228-4b4b-a05a-158b58a9ee68">
			<http:request method="GET" doc:name="Calling Artemis SYS API" doc:id="eb8615f0-efda-4b57-9123-a47a38ed8ddd" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.company.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
			</http:request>
		</try>  
	<ee:transform doc:name="payload" doc:id="9ada4c14-b20b-4127-ac69-aa8d4fe61aea">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table,	
	paging: payload.response.Table1
}]]></ee:set-payload>
					</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[payload.statusCode]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[payload.httpReasonPhrase]]></ee:set-variable>
			</ee:variables>
				</ee:transform>  
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="80d64549-9c09-46e9-9d24-e84c8760b917" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_agreements_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</flow>
	<flow name="company_documents_deliveries_get" doc:id="0fc5aa68-6db9-44a7-bd0c-2bbacde4c03a" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="c4fda6fa-aa4d-465a-9f13-25d571758951" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_agreements_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		 <set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@DocumentType=Deliveries" ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="cb512747-9085-403f-a3ea-4fd50c8774c6" variableName="parameters" />  
		 <try doc:name="Try Retrieving Company Documents" doc:id="9b311551-0592-4057-84d5-c36971e32373">
			<http:request method="GET" doc:name="Calling Artemis SYS API" doc:id="c8f5d59b-df93-4b3e-b912-abedbf9ec21c" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.company.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
			</http:request>
		</try>  
		 <ee:transform doc:name="payload" doc:id="8f2ad87f-40f2-47c9-b140-7fa6abf8afa9">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table,	
	paging: payload.response.Table1
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>  
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="98b4f8e1-b393-4a68-ad2d-9018e7a1187f" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_deliveries_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	
		</flow>
	<flow name="company_documents_invoices_get" doc:id="67e25e87-eaf6-4c45-8c75-520dbfab6a52" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="eae89559-5d9d-42c6-92e9-96d5fcdbde74" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_invoices_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		 <set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@DocumentType=Invoices" ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="c54a38bd-f516-413c-a297-08ef536ca11e" variableName="parameters" /> 
	 <try doc:name="Try Retrieving Company Documents" doc:id="8dd13e60-94ad-4e9b-b669-6c66df22f8c2">
			<http:request method="GET" doc:name="Calling Artemis SYS API" doc:id="11d460a7-6931-4829-83b6-0014acb4a37a" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.company.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
			</http:request>
		</try> 
		<ee:transform doc:name="payload" doc:id="5971acc1-7a21-4b23-8556-9382aa7c8a8c">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table,	
	paging: payload.response.Table1
}]]></ee:set-payload>
					</ee:message>
				</ee:transform> 
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="39a6abc2-79fa-4530-8bc1-53560dababcb" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-documents\company_documents_invoices_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	
		</flow>
</mule>
