<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="product_get" doc:id="5f81bb10-f460-42e8-b636-4c59944f9f17" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="a42f97a6-926a-494c-b96a-c3a88e6c9cbf" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-product\product_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Products" doc:id="c9603b76-21e5-4876-be73-61ad9c71166c" >
			<http:request method="GET" doc:name="Call Artemis SYS API" doc:id="ec8eaebf-ae14-4966-a6ee-3bb76062cd8a" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER" >
				<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
				<http:query-params ><![CDATA[#[%dw 2.0
var productName = "$(attributes.queryParams.name default '' as String)"
var productId = "$(attributes.queryParams.id default '' as String)"
var pageSize = "$(attributes.queryParams.pageSize default '' as String)"
var pageNumber = "$(attributes.queryParams.pageNumber default '' as String)"
output application/json
---
{
	database: Mule::p('cp.https.request.query.database'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectName: Mule::p('cp.https.request.query.objectName.products'),
	parameters: "@ProductID=$(productId);@ProductName=$(productName);@PageSize=$(pageSize);@PageNumber=$(pageNumber)"
}]]]></http:query-params>
			</http:request>
		</try>
		<ee:transform doc:name="payload" doc:id="1ea3ccea-f9ec-423f-8fc6-abee81d2b94d" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table as Array<Object>,
	paging: payload.response.Table1[0] as Object
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
payload.statusCode as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
payload.reasonPhrase]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="7138eb2e-5d6d-431c-9693-27097e2aad1b" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-product\product_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</flow>
</mule>
