<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd ">
    <http:listener-config name="HTTPS_Listener_config" doc:name="HTTP Listener config" doc:id="014e89ce-26aa-4bcf-acb3-e4d31b2d10b4">
        <http:listener-connection host="${https.listener.host}" port="${https.listener.port}" readTimeout="${https.listener.readTimeout}" connectionIdleTimeout="${https.listener.idleTimeout}" protocol="HTTPS" tlsContext="TLS_Context_Inbound" />
    </http:listener-config>
    <apikit:config name="3degreesArtemisExpAPI-config" api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:3degrees-artemis-exp-api:1.0.54:raml:zip:3degreesArtemisExpAPI.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus"  />
    <flow name="3degreesArtemisExpAPI-main">
        <http:listener path="/api/*" doc:name="/api/*" config-ref="HTTPS_Listener_config">
            <http:response statusCode="#[vars.httpStatus default 200]">
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[vars.httpStatus default 500]">
                <http:body><![CDATA[#[payload]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
        <ee:transform doc:name="Set vAttributes, vCorrelationId, vOrigCorrelationId, vTransactionId" doc:id="e6029d5c-71a2-4689-a6de-4ad430d7b24e">
            <ee:variables>
                <ee:set-variable variableName="vCorrelationId"><![CDATA[output application/json --- attributes.headers.'correlationId' default correlationId]]></ee:set-variable>
                <ee:set-variable variableName="vTransactionId"><![CDATA[output application/json --- attributes.headers.'x-transactionId' default correlationId]]></ee:set-variable>
                <ee:set-variable variableName="vAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
                <ee:set-variable variableName="vOrigCorrelationId"><![CDATA[output application/json --- attributes.headers.'correlationId' default correlationId]]></ee:set-variable>
                <ee:set-variable variableName="vMsgTimestamp"><![CDATA[%dw 2.0
output application/json
---
now()]]></ee:set-variable>
				<ee:set-variable variableName="request" ><![CDATA[%dw 2.0
output application/java
---
{
	method: attributes.method,
	path: attributes.maskedRequestPath,
	correlationID: correlationId
} ++ if ( !isEmpty(attributes.uriParams) ) {
	uriParameters: attributes.uriParams
} else {
} ++ if ( !isEmpty(attributes.queryParams) ) {
	queryParameters: attributes.queryParams
} else {
}]]></ee:set-variable>
            </ee:variables>
        </ee:transform>
        <apikit:router config-ref="3degreesArtemisExpAPI-config" />
    </flow>
    <flow name="put:\account:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-account-updated" doc:id="b0627ec3-a6b2-448a-b76f-ed969225e4e6" name="pf-on-artemis-account-updated" />
    </flow>
    <flow name="post:\account:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-account-created" doc:id="2722ee5e-1de0-4ec2-ae00-32dad698d444" name="pf-on-artemis-account-created" />
    </flow>
    <flow name="get:\notifyDelete:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-notify-account-delete" doc:id="de945e0a-0079-4c52-873b-e2041cf01271" name="pf-notify-artemis-account-delete" />
    </flow>
    <flow name="post:\confirms:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-invocation-confirm-event" doc:id="5dff5642-7c4d-466e-b378-c761b697db62" name="pf-on-artemis-invocation-confirm-event" />
    </flow>
    <flow name="put:\externalIDs:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-external-ids-update" doc:id="f035c4c7-e395-476e-8b8a-6427d819a096" name="pf-on-artemis-external-ids-update" />
    </flow>
    <flow name="put:\product:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-product-updated" doc:id="64950b1f-6467-47b5-ba09-af1e08bc7942" name="pf-on-artemis-product-updated" />
    </flow>
    <flow name="post:\product:application\json:3degreesArtemisExpAPI-config">
        <flow-ref doc:name="Flow Reference to pf-on-artemis-product-created" doc:id="b0c48881-63bd-4938-a8f2-5d72be9c3246" name="pf-on-artemis-product-created" />
    </flow>
    <flow name="get:\changed-documents:3degreesArtemisExpAPI-config">
       <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="49678b17-2e29-4263-863f-b51bbac144f8" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="company_changeddocuments_lastsync" doc:id="fe0a63fa-3f7d-49b6-a00b-8239386ce968" doc:description="system-flows\artemis\sf-documents\document_get" name="company_changeddocuments_lastsync" />
        <logger level="INFO" doc:name="LOG INFO: Outbound Response" doc:id="da84cbce-df87-4f24-9544-ab4ab30c75db" message="#[output application/json&#xA;---&#xA;payload]" />
    </flow>
    <flow name="get:\companies:3degreesArtemisExpAPI-config">
               <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="4d09f973-85fd-4527-b7c0-76ee25593292" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="8f88a773-f28a-4704-85b8-c1b4fcd91f1c" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Companies" doc:id="0156d8c5-aeb5-4ccf-ab36-1435ab101f75" name="company_get" />
        <ee:transform doc:name="Outbound Response" doc:id="9721d67e-86fb-4888-bc7f-513cc3c352af">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies-need-sync:3degreesArtemisExpAPI-config">
       <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="dadc4a03-4f9e-469a-b5e0-01e4cad43d9d" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="company_changedorders_lastsync" doc:id="26efbcc7-720f-41e9-bae6-3b724215c591" doc:description="system-flows\artemis\sf-documents\document_get" name="companies-need-sync-lastsync" />
        <logger level="INFO" doc:name="LOG INFO: Outbound Response" doc:id="ce5b7342-f9a1-4588-95c1-1cb98d9a22cc" message="#[output application/json&#xA;---&#xA;payload]" />
    </flow>
    <flow name="get:\documents\(documentID):3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="242a353e-d7a2-4f41-ae68-a38a99722d8b" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationId&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Document" doc:id="6d2a1adb-4f4d-4713-8c4d-3c0ff2c561f3" name="document_get" doc:description="system-flows\artemis\sf-documents\document_get" />
        <ee:transform doc:name="Outbound Response" doc:id="199b6544-d41b-4e0b-80db-404dff5f2224">
            <ee:message>
                <ee:set-payload><![CDATA[payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\products:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="bfb87255-6e8a-4bad-98f8-912c18faa7c7" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="0c015bf1-4eb6-4028-9876-9a324a66e960" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Products" doc:id="27b2b22a-5c96-4d2f-9632-75aa8ca3b96c" name="product_get" />
        <ee:transform doc:name="Outbound Response" doc:id="c36ee92a-19df-4df1-be44-5afaee2f0616">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\commodities:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="d6312ff3-7b96-4c9a-8032-7505fd0ad025" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Commodities" doc:id="713b4e46-3bb5-43a7-8e58-23e37c1b7659" name="company_transactions_commodities_get" />
        <ee:transform doc:name="Outbound Response" doc:id="441c0dd6-1974-4a50-a579-41f87acf3ba7">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---

	payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\documents\agreements:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="ec33cc4d-d634-44b6-a3cb-d790e919adf8" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="5f480389-d7fe-40e5-911f-8b24399fe44d" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Agreement Documents" doc:id="a2e113d3-af6f-4720-a895-7cf3cc2718f4" name="company_documents_agreements_get" />
        <ee:transform doc:name="Outbound Response" doc:id="44672db8-e83e-4b56-b538-eaf2050a8a80">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\documents\deliveries:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="596e68ea-7a3c-4232-875d-80fe1f158304" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="9770119d-941e-43f9-8d6a-71d3c11d04c5" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Delivery Documents" doc:id="844d8918-762c-415d-adf6-211cafd248d4" name="company_documents_deliveries_get" />
        <ee:transform doc:name="Outbound Response" doc:id="e0941eb8-77d7-41aa-aa59-9d216e27c85b">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\documents\invoices:3degreesArtemisExpAPI-config">
         <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="c77278b6-9199-43f1-8e54-5e2d40082c95" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="ae0f4d9a-67ab-4882-a226-35d28dacf048" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Invoice Documents" doc:id="360f680a-36fe-4cc1-a01b-dac83ac73a41" name="company_documents_invoices_get" />
        <ee:transform doc:name="Outbound Response" doc:id="bd31f107-e6f8-4723-8c9a-e092a08e61b1">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
payload]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\products:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="56793a69-d34c-4f7b-8530-fa91a8be20d7" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Products" doc:id="48d40266-ca35-48a1-8b98-b486c35cd3ba" name="company_transactions_products_get" />
        <ee:transform doc:name="Outbound Response" doc:id="26ebd0ad-439f-4abf-bdfe-e63f30acc2ca">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data default [],
	paging: payload.paging default {}
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\services:3degreesArtemisExpAPI-config">
        <ee:transform doc:name="Transform Message">
            <ee:variables>
                <ee:set-variable variableName="companyID">attributes.uriParams.'companyID'</ee:set-variable>
            </ee:variables>
        </ee:transform>
        <ee:transform doc:name="Transform Message">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
  data: [
    {
      project_name: "RE Strategy 2020",
      contract_sign_date: "2020-12-30",
      project_budget: 62000,
      project_status: "Completed",
      sales_team_contact: {
        name: "Lindsey Ziegler",
        email: "<EMAIL>",
        is_active: true,
        is_current_sales_member: true,
        images: {
          is_active_image: false,
          small: "https://3degrees.file.force.com/profilephoto/005/T",
          medium: "https://3degrees.file.force.com/profilephoto/005/M",
          large: "https://3degrees.file.force.com/profilephoto/005/F"
        }
      }
    }
  ],
  paging: {
    total: 1,
    pages: 1,
    page_size: 100,
    page: 1
  }
} as Object {encoding: "UTF-8", mediaType: "application/json"}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="ea3b6171-ad61-4733-932f-587f41ca98d0" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Transactions" doc:id="f9a0107f-846b-4627-bb69-dfae74415f59" name="company_transactions_get" />
        <ee:transform doc:name="Outbound Response" doc:id="da334aad-fa96-4a99-81ba-27f3a9ebe879">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data default [],
	paging: payload.paging default {}
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID):3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="0c4a0e71-7096-4bed-b84c-57356b683006" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Transaction" doc:id="d8f5e995-915c-41bb-abdf-47c7b1270fba" name="company_transactions_get" />
        <ee:transform doc:name="Outbound Response" doc:id="5aae3f31-f8c3-4e08-888d-df0ed50625e5">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data default [],
	paging: payload.paging default {}
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID)\commodities:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="6c359084-2ddb-408d-b64d-1b076c3128f9" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Transaction Commodities" doc:id="e6fe9004-46a2-4476-9e19-c1087c9185bc" name="company_transactions_commodities_get" />
        <ee:transform doc:name="Outbound Response" doc:id="e95a6910-764c-4361-85ab-205c8bbb7e2b">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data default [],
	paging: payload.paging default {}
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID)\documents\agreements:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="411362fa-76bf-4e86-86b4-5324206767c8" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Transaction Agreement Documents" doc:id="ac139a67-8dce-4d7f-9d2a-9fa437384620" name="company_transaction_documents_agreements_get" />
        <ee:transform doc:name="response" doc:id="d9f44276-8c63-4481-a57d-b0904369ef08">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data,
	paging: payload.paging
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID)\documents\deliveries:3degreesArtemisExpAPI-config">
         <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="efa93d8a-66e4-453c-812b-126be1a17aaa" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="e78892a8-f65d-4f43-9594-19b5a320e6c4" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Transaction Delivery Documents" doc:id="5cf24579-73f3-4322-b88c-699990843b9e" name="company_transaction_documents_deliveries_get" />
        <ee:transform doc:name="response" doc:id="2bdb771b-663a-4ad8-8d3f-db66209eabd6">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data,
	paging: payload.paging
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID)\documents\invoices:3degreesArtemisExpAPI-config">
         <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="67047a97-5aae-41f1-85f4-e9167389c177" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request" doc:id="f42ce40f-5e99-4d75-adc7-54b8136afdd0" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: vars.request,&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Transaction Invoice Documents" doc:id="666d4cdb-0c2b-47b2-927b-af142851673c" name="company_transaction_documents_invoices_get" />
        <ee:transform doc:name="response" doc:id="b48b4951-19dd-4e85-b105-0f63b5e9e193">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data,
	paging: payload.paging
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="get:\companies\(companyID)\transactions\(transactionID)\products:3degreesArtemisExpAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="38738688-2841-4ae6-a791-433ee058113a" message="#[%dw 2.0&#xA;output application/json&#xA;---&#xA;{&#xA; &quot;Message&quot;: &quot;INBOUND REQUEST&quot;,&#xA; &quot;Request&quot;: {&#xA;  method: vars.request.method,&#xA;  requestUri: vars.request.requestUri&#xA; },&#xA; &quot;CorrelationID&quot;: vars.request.correlationID&#xA;}]" />
        <flow-ref doc:name="Company Transaction Products" doc:id="5e78e2e4-286a-46d1-a42c-35e285aa1fec" name="company_transactions_products_get" />
        <ee:transform doc:name="Outbound Response" doc:id="1203a687-e462-43fa-9262-957118ca5f0a">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	data: payload.data default [],
	paging: payload.paging default {}
}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
</mule>
