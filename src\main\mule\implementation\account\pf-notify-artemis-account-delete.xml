<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-notify-artemis-account-delete"
		doc:id="a6b04780-3571-4b3e-81ad-6e288a14eb2e" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="0cedf5f6-6d5f-45fc-a18b-6de2a28c23be"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started",&#10;	"FlowName" : "pf-notify-artemis-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId &#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="d1653854-44d9-413b-a96e-215823d73c8e"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-notify-artemis-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="d75c8c66-73ff-44d7-9b63-164ffa9f0ccf"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-notify-artemis-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform
			doc:name="Set vArtemisId, vObjectType, vRequestAttributes,vSyncPriority"
			doc:id="df0d2075-10d7-41b9-a260-1fa419df3749">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": "ARTEMIS",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": (now() as LocalDateTime {format: "yyyy-MM-dd'T'HH:mm:ss.000'Z'"}),
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_EXP_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
				<ee:set-variable variableName="vObjectType"><![CDATA[%dw 2.0
output application/json
---
upper(attributes.queryParams.'objectType')]]></ee:set-variable>
				<ee:set-variable variableName="vArtemisId" ><![CDATA[attributes.queryParams.'counterpartyID']]></ee:set-variable>
				<ee:set-variable variableName="vSyncPriority" ><![CDATA[attributes.queryParams.'syncPriority']]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check enableSyncForArtemis" doc:id="8f720dea-45e1-47b1-8359-ad4e51b11b03" >
			<when expression="#[Mule::p('enableSyncForArtemis') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is enabled" doc:id="a0ac88bb-b267-41e4-ab97-28f005ca876f" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForArtemis is enabled", &#10;	"FlowName" : "pf-notify-artemis-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<http:request method="GET" doc:name="Fetch ENTERPRISE_ID from REF_ID" doc:id="6ee41a0e-2386-4b76-9fe8-b76d1db7c6c6" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]" target="vRefIdResponse">
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
			<http:query-params><![CDATA[#[output application/java
---
{
	"ARTEMIS_ID": vars.vArtemisId,
	"OBJECT_TYPE": vars.vObjectType
}]]]></http:query-params>
		</http:request>
				<choice doc:name="Checking Record present in REF_ID table" doc:id="28169e3b-7108-46bf-8262-dfc04715d7bd">
			<when expression="#[!isEmpty(vars.vRefIdResponse.response[0])]">
				<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="69474e18-00da-4a1c-84a2-4b3b099dba88">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "DELETE",
    "SOURCE": "ARTEMIS",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID": vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default null, 
    "PAYLOAD": null,
    "PRIORITY": (vars.vSyncPriority default "0") as Number,
    "OBJECT_TYPE": "ACCOUNT",
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null
  }
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAIL" doc:id="eb7b69b3-99ef-4f11-be79-ccbc4d6a1ec6" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionDetails.path')]" target="vInsertTransactionResponse">
			<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
		</http:request>
						<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="8754ed05-224b-429e-ba46-ea0178e9d2be" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')]" target="vSyncRecordResponse" >
							<http:body ><![CDATA[#[{}]]]></http:body>
							<http:headers ><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
						</http:request>
						<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="3105428b-1e6f-4bcf-af2c-ab69955193f6" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-notify-artemis-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="54606adc-88a9-465d-903f-d22d829203a0">
							<ee:message>
								<ee:set-payload><![CDATA[%dw 2.0
output application/json
--- 
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "message": "Request for delete notification has been submitted for validation and processing."
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables>
								<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: Recod Not Exists in REF_ID Table" doc:id="0c05d164-3e7a-4add-b038-d39e96d94ba3" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Record does not exist in REF_ID table",&#10;	"FlowName" : "pf-notify-artemis-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="4ff36c58-8336-4ab3-aca8-65b0e03c5a67" >
							<ee:message >
								<ee:set-payload ><![CDATA[%dw 2.0
output application/json 
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
    "message": "Record will not be processed for syncing",
    "description": "Record does not exist in REF_ID table",
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
			</otherwise>
		</choice>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is disabled" doc:id="62b44f70-5710-4f17-9ec8-15ff7b999146" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForArtemis is disabled", &#10;	"FlowName" : "pf-notify-artemis-account-delete", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="708030d4-e437-4009-a3a5-fec742df4b3d" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForArtemis is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="20343d4a-3727-49e0-abca-3f7a427b5b70"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-notify-artemis-account-delete",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="47f72374-2c07-4c5b-8dad-d7b8a75f9a36"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-notify-artemis-account-delete",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/notifyDelete"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="f03b0f78-1f29-4a14-81c2-8d25a31d9d9b"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-notify-artemis-account-delete",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
