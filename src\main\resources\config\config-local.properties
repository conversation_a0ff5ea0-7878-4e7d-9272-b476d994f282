### API ###
api.autodiscoveryId=18521981

### HTTPS Listener ###
https.listener.keystore.path=certificates/keystore-local.jks
https.listener.truststore.path=certificates/truststore-local.jks

### HTTPS Request Mule API###
https.request.muleApi.truststore.path=certificates/truststore-local.jks

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.host=transactiondb-sys-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.transactionDBSysApi.port=443
https.request.transactionDBSysApi.connectionTimeout=30000
https.request.transactionDBSysApi.responseTimeout=30000
https.request.transactionDBSysApi.reconnection.frequency=1000
https.request.transactionDBSysApi.reconnection.attempts=3

### HTTPS Request syncPrc API ###
https.request.syncPrcApi.host=sync-prc-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.syncPrcApi.port=443
https.request.syncPrcApi.basePath=/
https.request.syncPrcApi.connectionTimeout=30000
https.request.syncPrcApi.responseTimeout=30000
https.request.syncPrcApi.reconnection.frequency=1000
https.request.syncPrcApi.reconnection.attempts=3

### HTTPS Request orderPrc API ###
https.request.orderPrcApi.host=order-prc-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.orderPrcApi.port=443
https.request.orderPrcApi.basePath=/
https.request.orderPrcApi.connectionTimeout=30000
https.request.orderPrcApi.responseTimeout=30000
https.request.orderPrcApi.reconnection.frequency=1000
https.request.orderPrcApi.reconnection.attempts=3

### Enable Sync for Artemis ###
enableSyncForArtemis=1

### Artemis API's Endpoints ###
https.request.artemisSysApi.host=artemis-sys-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.artemisSysApi.port=443
https.request.artemisSysApi.basePath=/
https.request.artemisSysApi.idleTimeout=30000
https.request.artemisSysApi.responseTimeout=60000
https.request.artemisSysApi.maxConnections=-1
https.request.artemisSysApi.reconnection.frequency=1500
https.request.artemisSysApi.reconnection.attempts=3
https.request.artemisSysApi.truststore.path=certificates/truststore-dev.jks