### HTTPS Listener ###
https.listener.host=0.0.0.0
https.listener.port=8081
https.listener.readTimeout=30000
https.listener.idleTimeout=30000

### Error definition ###
errorCodeMessage.apikit.badRequest.code=400
errorCodeMessage.apikit.badRequest.description=Bad Request
errorCodeMessage.apikit.notFound.code=404
errorCodeMessage.apikit.notFound.description=Not Found
errorCodeMessage.apikit.methodNotAllowed.code=405
errorCodeMessage.apikit.methodNotAllowed.description=Method Not Allowed
errorCodeMessage.apikit.notAcceptable.code=406
errorCodeMessage.apikit.notAcceptable.description=Not Acceptable
errorCodeMessage.apikit.unsupportedMediaType.code=415
errorCodeMessage.apikit.unsupportedMediaType.description=Unsupported Media Type
errorCodeMessage.apikit.notImplemented.code=501
errorCodeMessage.apikit.notImplemented.description=Not Implemented

### DB SYS API ###
https.request.dbSysApi.refId.path=/api/REF_ID
https.request.dbSysApi.transactionDetails.path=/api/TRANSACTION
https.request.dbSysApi.transactionTaskDetails.path=/api/TRANSACTION_TASK
https.request.dbSysApi.configLookup.path=/api/CONFIG_LOOKUP

### SyncPrc API's endpoints###
https.request.syncPrcApi.syncRecords.path=/api/syncRecords

### OrderPrc API's endpoints ###
https.request.orderPrcApi.confirms.path=/api/confirms
https.request.orderPrcApi.bills.path=/api/bills
https.request.orderPrcApi.billingSchedules.path=/api/billingSchedules
https.request.orderPrcApi.externalId.path=/api/externalIDs
https.request.syncPrcApi.syncProducts.path=/api/syncProducts

# ARTEMIS API: Climate Portal
cp.https.request.query.database=Artemis
cp.https.request.query.schema=cp
cp.https.request.query.cp.schema=cp
cp.https.request.query.objectType.table=Table
cp.https.request.query.objectType.view=View
cp.https.request.query.objectType.sp=StoredProcedure
cp.https.request.query.objectName.companies=tdp_Companies
cp.https.request.query.objectName.products=tdp_Products
cp.https.request.query.objectName.companies.transactions=tdp_CompanyTransactions
cp.https.request.query.objectName.companies.transactions.commodities=tdp_CompanyTransactionsCommodities
cp.https.request.query.objectName.companies.transactions.products=tdp_CompanyTransactionsProducts
cp.https.request.query.schema.company.documents=erp
cp.https.request.query.objectName.company.documents=tdp_Documents
cp.https.request.query.schema.documents=erp
cp.https.request.query.objectName.documents=tdp_Document_Find
cp.https.request.query.objectName.opportunities=sf_Opportunity
cp.https.request.query.objectName.companies.toSync=tdp_companies_to_sync
cp.https.request.query.objectName.documents.changedate=tdp_GET_Documents_ChangeDate
