<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
	xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd 
http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd">
    
      
    <tls:context name="TLS_Context_Inbound"
		doc:name="TLS Context" doc:id="bbb725bc-18e5-447f-9d23-c1f48e9c7d07">
		<tls:key-store type="jks"
			path="${https.listener.keystore.path}"
			keyPassword="${secure::https.listener.keystore.keyPassword}"
			password="${secure::https.listener.keystore.password}" />
	</tls:context>
	<!-- <api-gateway:autodiscovery
		apiId="${api.autodiscoveryId}" ignoreBasePath="true"
		doc:name="API Autodiscovery"
		doc:id="a8adbc82-cfd9-40e8-a647-45033e81b260"
		flowRef="3degreesArtemisExpAPI-main" /> -->
	<tls:context name="TLS_Context_Mule_Api_Outbound" doc:name="TLS Context" doc:id="e8f84c89-d9e0-415d-bf8a-56554098619c" >
		<tls:trust-store path="${https.request.muleApi.truststore.path}" password="${secure::https.request.muleApi.truststore.password}" type="jks"/>
	</tls:context>
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="f59d1927-d252-4bcc-b9cb-a0622a037fe2"
		file="config\config-common.properties" />
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="8d0907dc-36e8-481c-833d-6f2614642a75"
		file="config\config-${mule.env}.properties" />
	<secure-properties:config
		name="Secure_Properties_Config" doc:name="Secure Properties Config"
		doc:id="9ca4b267-6aec-41b7-b788-a5e670637d70"
		file="config\config-${mule.env}-secure.properties" key="${mule.key}">
		<secure-properties:encrypt
			algorithm="Blowfish" />
	</secure-properties:config>
	<configuration doc:name="Configuration"
		doc:id="d5b3fe28-0fd7-4198-87cb-fd644245a2aa"
		defaultErrorHandler-ref="global-error-handler" />
	<!-- <db:config name="Database_Config_Transactions" doc:name="Database Config" 
		doc:id="a2e12a0f-09c4-4a74-b2a6-68f1c6927889" > <db:my-sql-connection host="${secure::db.host}" 
		port="${db.port}" user="${secure::db.username}" database="${secure::db.database}" 
		/> </db:config> -->
	<http:request-config
		name="HTTPS_Request_Transaction_DB_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="c6434c25-82cc-4c05-a935-349cfbb8dd91">
		<http:request-connection
			host="${https.request.transactionDBSysApi.host}" protocol="HTTPS"
			port="${https.request.transactionDBSysApi.port}"
			connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}" tlsContext="TLS_Context_Mule_Api_Outbound">
			<reconnection>
				<reconnect
					frequency="${https.request.transactionDBSysApi.reconnection.frequency}"
					count="${https.request.transactionDBSysApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config>
	
	<http:request-config
		name="HTTPS_Request_Sync_Prc_API"
		doc:name="HTTP Request configuration"
		doc:id="79235b72-0a0e-4125-93be-41615a276966" basePath="#[p('https.request.syncPrcApi.basePath')]">
		<http:request-connection
			host="${https.request.syncPrcApi.host}" protocol="HTTPS"
			port="${https.request.syncPrcApi.port}"
			connectionIdleTimeout="${https.request.syncPrcApi.connectionTimeout}" tlsContext="TLS_Context_Mule_Api_Outbound">
			<reconnection>
				<reconnect
					frequency="${https.request.syncPrcApi.reconnection.frequency}"
					count="${https.request.syncPrcApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.syncPrcApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.syncPrcApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config>
	<http:request-config
		name="HTTPS_Request_Order_Prc_API"
		doc:name="HTTP Request configuration"
		doc:id="77ec817d-70d8-4936-9bf2-643b49c3b5db" basePath="#[p('https.request.orderPrcApi.basePath')]" responseTimeout="${https.request.orderPrcApi.responseTimeout}">
		<http:request-connection
			host="${https.request.orderPrcApi.host}" protocol="HTTPS"
			port="${https.request.orderPrcApi.port}"
			connectionIdleTimeout="${https.request.orderPrcApi.connectionTimeout}" tlsContext="TLS_Context_Mule_Api_Outbound">
			<reconnection>
				<reconnect
					frequency="${https.request.orderPrcApi.reconnection.frequency}"
					count="${https.request.orderPrcApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.orderPrcApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.orderPrcApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config>
	
	<http:request-config
		name="HTTPS_Request_Artemis_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="7137d3cc-75ee-4e6d-878f-b93799e10a79"
		responseTimeout="#[p('https.request.artemisSysApi.responseTimeout')]"
		basePath="#[p('https.request.artemisSysApi.basePath')]">
		<http:request-connection protocol="HTTPS"
			host="#[p('https.request.artemisSysApi.host')]"
			port="#[p('https.request.artemisSysApi.port')]"
			connectionIdleTimeout="${https.request.artemisSysApi.idleTimeout}" maxConnections="${https.request.artemisSysApi.maxConnections}" tlsContext="TLS_Context_Inbound">
			<reconnection >
				<reconnect frequency="${https.request.artemisSysApi.reconnection.frequency}" count="${https.request.artemisSysApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.artemisSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.artemisSysApi.headers.clientSecret')]" />
			<http:default-header key="correlationId" value="#[vars.vCorrelationId]" />
			<http:default-header key="x-businessKey" value='#[vars.vBusinessKey default ""]' />
		</http:default-headers>
	</http:request-config>
	
	<api-gateway:autodiscovery
		apiId="${api.autodiscoveryId}" ignoreBasePath="true"
		doc:name="API Autodiscovery"
		doc:id="a8adbc82-cfd9-40e8-a647-45033e81b260"
		flowRef="3degreesArtemisExpAPI-main" />
</mule>
