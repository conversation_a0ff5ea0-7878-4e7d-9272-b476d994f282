# Apisero_Artemis_EXP_API: feature branch

## INTERFACE

[interface](documentation/interface.md) API Router & Endpoints

## COMMON

[global](documentation/global.md) Global Configuration Elements  
[global-error-handler](documentation/global-error-handler.md) Global Error Handler 

## IMPLEMENTATION

[pf-notify-artemis-account-delete](documentation/pf-notify-artemis-account-delete.md) Insert Queued Artemis Account Record for Deletion in `Enterprise_ID`  
[pf-on-artemis-account-created](documentation/[pf-on-artemis-account-created.md) Insert Queued Artemis Account Record for Creation in `Enterprise_ID`  
[pf-on-artemis-account-updated](documentation/[pf-on-artemis-account-updated.md) Insert Queued Artemis Account Record for Updating in `Enterprise_ID`  

---

## CONFIGURATION

[config](documentation/config.md) Configuration
