# Artemis Expenses API

This project is a C# ASP.NET Core Web API for managing Artemis expenses. It is inspired by Mule flows and supports endpoints for account, product, company, and transaction management.

## Setup
- Environment configuration files are located in `../src/main/resources/config`.
- Certificates for secure communication are in `../src/main/resources/certificates`.

## Usage
- Build: `dotnet build`
- Run: `dotnet run`

## Next Steps
- Integrate environment config and certificates
- Implement API endpoints

Refer to copilot-instructions.md for progress tracking.
