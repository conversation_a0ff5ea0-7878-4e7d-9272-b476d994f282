<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-on-artemis-bill-updated" doc:id="4662bb0a-9426-41f3-b96d-f5a8fb2914b3" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="d34abdbf-3098-41c2-91dc-9b91037cb0aa" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "sf-on-artemis-bill-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="f1fcf680-71dd-410c-bdb3-43e184e31939" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "sf-on-artemis-bill-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="d1442f83-9012-438a-994f-748f154e9c29" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "sf-on-artemis-bill-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<http:request method="PUT" doc:name="Call OrderPrc to process record" doc:id="96eaed5a-20be-4440-a0ee-aac84e891492" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.bills.path')]">
					<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "artemis-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vCorrelationId,
	"sourceId" : "artemis-exp-api",
	"x-businessKey": vars.businessKey
}]]]></http:headers>
				</http:request>
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="aeaf5187-787a-46de-9a78-c1a0b314d815" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "sf-on-artemis-bill-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="418b6e38-3073-4813-8097-e91c02615133" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "sf-on-artemis-bill-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="a6d9213a-d8de-4213-9b3e-15d598541ccc" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "sf-on-artemis-bill-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />

	</sub-flow>
</mule>
