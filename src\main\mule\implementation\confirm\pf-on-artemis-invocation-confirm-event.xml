<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd 
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<flow name="pf-on-artemis-invocation-confirm-event" doc:id="6c8860b6-11f9-410e-9bf1-cbc16e57ee09" >
		<ee:transform doc:name="vCorrelationId, businessKey" doc:id="d0ad3b25-d06d-4251-a720-6d977a346565" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vCorrelationId" ><![CDATA[payload.transactionID default vars.vCorrelationId]]></ee:set-variable>
				<ee:set-variable variableName="businessKey" ><![CDATA[payload.'payload'.confirm.action ++ "-ConfirmID:" ++ payload.'payload'.confirm.confirmID]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="cc7fcdb4-1b8e-4c07-9383-7b2e49806632" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<ee:transform doc:name="Set vConfirm" doc:id="41293944-f4fa-4ccf-a523-db4e7cead2bb" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vConfirm" ><![CDATA[%dw 2.0
output application/json
---
payload.'payload']]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check action" doc:id="2635ab72-aa6b-41d4-871c-05c68b551d78" >
			<when expression='#[upper(vars.vConfirm.confirm.action) ~= "CONFIRM"]'>
				<choice doc:name="Check operation" doc:id="6ac006a4-487d-48ff-aec1-0a2c7db73c08">
					<when expression='#[vars.vConfirm.confirm.operation == "CREATE"]'>
						<flow-ref doc:name="Flow Reference to sf-on-artemis-confirm-created" doc:id="4815fcc8-5821-4896-9512-815b96f1f916" name="sf-on-artemis-confirm-created" />
					</when>
					<when expression='#[vars.vConfirm.confirm.operation == "UPDATE" or isEmpty(vars.vConfirm.confirm.operation)]'>
						<flow-ref doc:name="Flow Reference to sf-on-artemis-confirm-updated" doc:id="ab02ae44-beaf-480b-a26e-abedba476966" name="sf-on-artemis-confirm-updated" />
					</when>
					<otherwise>
						<logger level="INFO" doc:name="LOG INFO: Discard Confirm" doc:id="8149c51b-14e5-4039-8ca2-4aef06f2f533" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : (vars.vConfirm.confirm.operation default "") ++ " is not supported for Confirm Object", &#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
					</otherwise>
				</choice>
			</when>
			<when expression='#[upper(vars.vConfirm.confirm.action) ~= "INVOICE"]'>
				<flow-ref doc:name="Flow Reference to sf-on-artemis-invoice-requested" doc:id="a4e9bdf0-942a-4746-a005-14d63921e3e5" name="sf-on-artemis-invoice-requested" />
			</when>
			<when expression='#[upper(vars.vConfirm.confirm.action) ~= "BILL"]'>
				<choice doc:name="Check operation" doc:id="2e36e8f7-f546-425c-a283-123fcb23fd33">
					<when expression='#[vars.vConfirm.confirm.operation == "UPDATE"]'>
						<flow-ref doc:name="Flow Reference to sf-on-artemis-bill-updated" doc:id="76cc9982-777d-45b6-937c-328b148f9c2b" name="sf-on-artemis-bill-updated" />
					</when>
					<when expression='#[vars.vConfirm.confirm.operation == "CREATE"]'>
						<flow-ref doc:name="Flow Reference to sf-on-artemis-bill-created" doc:id="53a63a1c-3ae9-4bf6-a3fa-7812452aee0b" name="sf-on-artemis-bill-created" />
					</when>
					<otherwise>
						<logger level="INFO" doc:name="LOG INFO: Discard Bill" doc:id="ae1e0ad1-895f-4cfb-bbb7-55b09a9764cb" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : (vars.vConfirm.confirm.operation default "") ++ " is not supported for Bill Object",&#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
					</otherwise>
				</choice>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: Not a valid action" doc:id="793dbbaa-7516-47cd-bd5b-194fdb8c11a7" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : (vars.vConfirm.confirm.action default "") ++ " is not supported.",&#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
			</otherwise>
		</choice>
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="5a0407a9-571f-4609-9936-374d629fc23a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<error-handler >
			<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="882fefd3-c0ef-4189-85c7-7f22eb637da6" type="ANY">
				<ee:transform doc:name="Set vError" doc:id="1588c175-89ae-41f4-ab74-50894cab577c" >
					<ee:message />
					<ee:variables >
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
error.'errorMessage'.'payload']]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<logger level="ERROR" doc:name="LOG ERROR: Exception while processing event" doc:id="3f81a594-01a2-4f17-8c27-f508f6dedd45" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Exception while calling order-prc-api", &#10;	"FlowName" : "pf-on-artemis-confirm-or-invoice-event", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"ErrorMessage": vars.vError.response.message,&#10;	"ErrorDetails": vars.vError.response.details&#10;}]'/>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="f32138da-8df8-418b-9c09-471d82675fc3" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
vars.vError]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[(vars.vError.code default "500") as Number ]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
		</error-handler>
	</flow>
</mule>
