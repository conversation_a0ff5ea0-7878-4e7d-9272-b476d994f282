<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-on-artemis-external-ids-update" doc:id="8782ca32-8bb2-44d0-8c01-6769c835ff84" >
		<http:request method="GET" doc:name="Retrieve Record from TRANSACTION_DETAILS" doc:id="336a3937-6ca5-484e-84c6-8f811cd5aa50" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
			<http:query-params><![CDATA[#[output application/java
---
{
	"CORRELATION_ID" : "'" ++ (payload.previousTransactionID default "-1") ++ "'"
}]]]></http:query-params>
		</http:request>
		<choice doc:name="Choice" doc:id="12193026-1618-436b-997c-3eaf81535465" >
			<when expression='#[output application/json --- payload.syncStatus != "SUCCESS"]'>
				<logger level="INFO" doc:name="LOG INFO: Received Error Response From Artemis" doc:id="2fa004a2-f366-45ba-8189-30d5eb32f9d1" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Received Error Response From Artemis", &#10;	"FlowName" : "pf-on-artemis-external-ids-update", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
				<set-variable value='#[%dw 2.0&#10;import * from dw::Runtime&#10;import * from dw::core::Arrays&#10;output application/json&#10;---&#10;if(vars.vTransactionResponse.response[0].OBJECT_TYPE ~= "DEAL") "CONFIRM"&#10;else if(vars.vTransactionResponse.response[0].OBJECT_TYPE ~= "INVOICE") "INVOICE"&#10;else fail("EXCEPTION_MESSAGE_START:Resource is not implemented for updating Artemis generated Ids for " ++ (vars.vTransactionResponse.response[0].OBJECT_TYPE default "") ++ " in target systems:EXCEPTION_MESSAGE_END")]' doc:name="vParentObject" doc:id="1120783b-c25c-4592-b74c-557af1ede0c4" variableName="vParentObject" />
				<set-variable value='#[%dw 2.0&#10;output application/json&#10;---&#10;vars.vParentObject ++ " | PreviousTransactionID: " ++ payload.previousTransactionID ++ " | EnterpriseID: " ++ (vars.vTransactionResponse.response[0].ENTERPRISE_ID default "")]' doc:name="vBusinessKey" doc:id="b9455bb1-1d47-46cf-98a3-1e423e74148c" variableName="vBusinessKey" />
			</when>
			<when expression="#[output application/json --- isEmpty(payload.payload.recordIDs)]">
				<logger level="INFO" doc:name="LOG INFO: No New Object Is Created" doc:id="37bf5526-99c1-4a5f-9f19-03916cec14aa" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Received success response from Artemis, no new object is created", &#10;	"FlowName" : "pf-on-artemis-external-ids-update", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
				<set-variable value='#[%dw 2.0&#10;import * from dw::Runtime&#10;import * from dw::core::Arrays&#10;output application/json&#10;---&#10;if(vars.vTransactionResponse.response[0].OBJECT_TYPE ~= "DEAL") "CONFIRM"&#10;else if(vars.vTransactionResponse.response[0].OBJECT_TYPE ~= "INVOICE") "INVOICE"&#10;else fail("EXCEPTION_MESSAGE_START:Resource is not implemented for updating Artemis generated Ids for " ++ (vars.vTransactionResponse.response[0].OBJECT_TYPE default "") ++ " in target systems:EXCEPTION_MESSAGE_END")]' doc:name="vParentObject" doc:id="ff5178a1-0729-4ca9-8441-9489cb0e55cf" variableName="vParentObject" />
				<set-variable value='#[%dw 2.0&#10;output application/json&#10;---&#10;vars.vParentObject ++ " | PreviousTransactionID: " ++ payload.previousTransactionID ++ " | EnterpriseID: " ++ (vars.vTransactionResponse.response[0].ENTERPRISE_ID default "")]' doc:name="vBusinessKey" doc:id="97088927-90b2-4c6f-8e67-9353dbaf1a4d" variableName="vBusinessKey" />
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: New Object Is Created" doc:id="ee79ca08-2fc7-4df5-90dc-90df5ccfdc53" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Received success response from Artemis, new object is created", &#10;	"FlowName" : "pf-on-artemis-external-ids-update", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
				<set-variable value='#[%dw 2.0&#10;import * from dw::Runtime&#10;import * from dw::core::Arrays&#10;output application/json&#10;---&#10;if(payload.payload.recordIDs.*objectType some (($ ~= "DEAL") or ($ ~= "AGREEMENT") or ($ ~= "ORDERPOSITION"))) "CONFIRM"&#10;else if(payload.payload.recordIDs.*objectType some (($ ~= "INVOICE") or ($ ~= "INVOICEITEM") or ($ ~= "INVOICEITEMPOSITION"))) "INVOICE"&#10;else fail("EXCEPTION_MESSAGE_START:Resource is not implemented for updating Artemis generated Ids for either of " ++ (payload.payload.recordIDs.*objectType default [] joinBy ", ") ++ " in target systems:EXCEPTION_MESSAGE_END")]' doc:name="vParentObject" doc:id="fc4e169a-f428-4685-8609-a2b6cbf1193d" variableName="vParentObject" />
				<set-variable value='#[%dw 2.0&#10;var enterpriseId = (&#10;	if(vars.vParentObject == "CONFIRM") (payload.payload.recordIDs filter(($).objectType ~= "DEAL"))[0].universalID&#10;	else if(vars.vParentObject == "INVOICE") (payload.payload.recordIDs filter(($).objectType ~= "INVOICE"))[0].universalID&#10;	else ""&#10;)&#10;output application/json&#10;---&#10;vars.vParentObject ++ " | PreviousTransactionID: " ++ payload.previousTransactionID ++ " | EnterpriseID: " ++ (enterpriseId default "")]' doc:name="vBusinessKey" doc:id="0398f3fb-afe3-4250-92aa-82b99d2897fc" variableName="vBusinessKey" />
			</otherwise>
		</choice>
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="83b66b2f-42f5-4b38-80b6-f5d61afd18f7" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-artemis-external-ids-update", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="2ad832ea-5b4a-4b72-a2e1-8928fa17e3b2" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-on-artemis-external-ids-update",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/externalIDs",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Inbound Request Payload" doc:id="47eb6f95-f045-45d9-9838-b8751a1e39e4" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request Payload",&#10;	"FlowName" : "pf-on-artemis-external-ids-update",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/externalIDs",&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vInvoiceRecord" doc:id="04e4dc50-0ac9-42f0-ae1e-e873b6d2865b" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vInvoiceRecord" ><![CDATA[%dw 2.0
output application/json
var invoiceRecord = read(vars.vTransactionResponse.response.PAYLOAD[0], "application/json")
---
{    
	"recordName": invoiceRecord.response.invoice.tranId,
	"recordID": invoiceRecord.response.invoice.netSuiteInvoiceId,
	"lineItems": invoiceRecord.response.invoice.invoiceLines map ((items) ->
		{
			"invoiceItemNumb": items.invoiceItemNumb,
			"lineID": items.line
		}
	)
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<set-variable value="#[output application/json&#10;var targetSystem = if(vars.vParentObject ~= &quot;CONFIRM&quot;) &quot;SALESFORCE&quot; else if(vars.vParentObject ~= &quot;INVOICE&quot;) &quot;NETSUITE&quot; else &quot;null&quot;&#10;var object = if(vars.vParentObject ~= &quot;CONFIRM&quot;) &quot;OPPORTUNITY&quot; else if(vars.vParentObject ~= &quot;INVOICE&quot;) &quot;INVOICE&quot; else &quot;null&quot;&#10;---&#10;p('https.request.orderPrcApi.externalId.path') ++ &quot;?target=&quot; ++ targetSystem ++ &quot;&amp;object=&quot; ++ object]" doc:name="vRequestEndpoint" doc:id="216d2b91-04a4-4fe7-8a89-2e370a377af8" variableName="vRequestEndpoint"/>
		<set-variable value='#[%dw 2.0&#10;import * from dw::Runtime&#10;import * from dw::core::Arrays&#10;output application/json&#10;---&#10;if(vars.vParentObject ~= "CONFIRM") (&#10;	payload update {&#10;    	case req at .payload -&gt; {&#10;    		"recordIDs": ((payload.payload.recordIDs default []) + (&#10;    			if(!(payload.payload.recordIDs.*objectType some ($ ~= "DEAL"))) {&#10;					"objectType": "DEAL",&#10;					"recordID": "",&#10;					"universalID": vars.vTransactionResponse.response[0].ENTERPRISE_ID default ""&#10;				}&#10;				else {}&#10;			)) - {}&#10;		}&#10;	}&#10;) else if(vars.vParentObject ~= "INVOICE") (&#10;	payload update {&#10;    	case req at .payload -&gt; {&#10;    		"recordIDs": ((payload.payload.recordIDs default []) + (&#10;    			if(!(payload.payload.recordIDs.*objectType some ($ ~= "INVOICE"))) {&#10;					"objectType": "INVOICE",&#10;					"recordID": "",&#10;					"universalID": vars.vTransactionResponse.response[0].ENTERPRISE_ID default ""&#10;				}&#10;				else {}&#10;			)) - {}&#10;		}&#10;	}&#10;) ++ ("transactionDBInvoiceRecord": vars.vInvoiceRecord default "") else {&#10;	&#10;}]' doc:name="vRequestPayload" doc:id="149cf871-f82d-466d-818d-5b88f8b1a4d3" variableName="vRequestPayload"/>
		<http:request method="PUT" doc:name="Call OrderPrc to process record" doc:id="066f4fb5-dde3-4221-bd8e-c690449c0e6c" config-ref="HTTPS_Request_Order_Prc_API" path="#[vars.vRequestEndpoint]" target="vUpdateIdsResponse">
					<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="CUSTOM:DATA_VALIDATION_ERROR" />
			<http:body ><![CDATA[#[vars.vRequestPayload]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "artemis-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vCorrelationId,
	"sourceId" : "artemis-exp-api",
	"x-businessKey" : vars.vBusinessKey
}]]]></http:headers>
				</http:request>
		<set-payload value='#[output application/json&#10;---&#10;{&#10;	"sourceSystem": "ARTEMIS",&#10;	"transactionID": payload.transactionID,&#10;    "previousTransactionID": payload.previousTransactionID,&#10;	"syncStatus": vars.vUpdateIdsResponse.status default "SUCCESS",&#10;	"syncMessage": ((vars.vUpdateIdsResponse.response.message default "") ++ " | " ++ (vars.vUpdateIdsResponse.response.details default ""))&#10;}]' doc:name="Set Payload" doc:id="2d467932-19f6-4227-aff8-5683a64e38e4" />
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="210166af-05ce-4444-bf02-da482a67ef4b" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "pf-on-artemis-external-ids-update",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/externalIDs",&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"UpdateIDsResponse": payload&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="0f5cfb4a-79be-446e-a2e1-7d074cb77cc4" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "pf-on-artemis-external-ids-update",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/externalIDs",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="7426231b-8a5f-49ad-93b6-f384f453dc57" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "pf-on-artemis-external-ids-update", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
				<error-handler >
			<on-error-continue enableNotifications="false" logException="false" doc:name="On Error Continue" doc:id="87cb373c-946f-4bb7-9c5d-18e923999d78" type="ANY" when='#[%dw 2.0 import * from dw::core::Strings output json --- ((error.description startsWith "\"EXCEPTION_MESSAGE_START:") and (!isEmpty(substringBefore(substringAfter(error.description default "", "EXCEPTION_MESSAGE_START:"), ":EXCEPTION_MESSAGE_END"))))]' >
				<ee:transform doc:name="Set vError" doc:id="9e27e70a-2d4a-4e21-9929-5b3820942871" >
					<ee:message />
					<ee:variables >
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
substringBefore(substringAfter(error.description default "", "EXCEPTION_MESSAGE_START:"), ":EXCEPTION_MESSAGE_END")]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="89fa9fa2-094b-4cd0-9899-fc2cbb0bfa73" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	"sourceSystem": "ARTEMIS",
	"transactionID": payload.transactionID,
    "previousTransactionID": payload.previousTransactionID,
	"syncStatus": "FAILURE",
	"syncMessage": vars.vError
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
			<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="74b63f5b-f2f4-4efc-b334-7874d29abf22" type="CUSTOM:DATA_VALIDATION_ERROR" >
				<ee:transform doc:name="Set vError" doc:id="717ca36a-e069-4865-987e-25aeb436a10e" >
					<ee:message />
					<ee:variables >
						<ee:set-variable variableName="vError" ><![CDATA[%dw 2.0
output application/json
---
error.'errorMessage'.'payload']]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="e22dd17a-cca5-422f-8ab2-4a587dad465d" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	"sourceSystem": "ARTEMIS",
	"transactionID": payload.transactionID,
    "previousTransactionID": payload.previousTransactionID,
	"syncStatus": vars.vError.status default "FAILURE",
	"syncMessage": vars.vError.response.message
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</on-error-continue>
		</error-handler>
	</flow>
</mule>
