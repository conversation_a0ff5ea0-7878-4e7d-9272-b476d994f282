<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:aggregators="http://www.mulesoft.org/schema/mule/aggregators" xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	xmlns:batch="http://www.mulesoft.org/schema/mule/batch"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/batch http://www.mulesoft.org/schema/mule/batch/current/mule-batch.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/aggregators http://www.mulesoft.org/schema/mule/aggregators/current/mule-aggregators.xsd">
	<flow name="company_transaction_documents_agreements_get" doc:id="056cef79-cce5-452e-bc17-571b20de8aa5" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="e2e37727-5ac8-444a-9f78-8c217a296437" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "company_transaction_documents_agreements_get",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		 <set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var transactionID = attributes.uriParams.transactionID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@ConfirmID=" ++ transactionID as String ++&#10;";@DocumentType=Agreements" as String ++&#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="5740e863-2892-476b-be0c-42e663b1f445" variableName="parameters" />
		 <!-- <http:request method="GET" doc:name="EXEC erp_tdp_Documents" doc:id="8992a429-92bf-4aa0-86a1-80491e4cc4f8" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="NEVER" responseTimeout="60000">
				<reconnect />
				<http:body><![CDATA[""]]></http:body>
				<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	"schema" : "erp",
	"objectType" : "StoredProcedure",
	"objectName" : "tdp_Documents",
	"parameters" : vars.parameters,
	"database" : "Artemis"
}]]]></http:query-params>
			</http:request>  --> 
			<http:request method="GET" doc:name="Call Artemis SYS api" doc:id="7b9da547-32d8-42d5-a7b2-a098a9b7be76" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		 <choice doc:name="Choice" doc:id="ddb3d22b-60af-4d29-b999-6890795ba933">
			<when expression='#[(sizeOf(payload.response.Table)) &lt; 1]'>
				<ee:transform doc:name="Transform Message" doc:id="ed9aeaf1-2141-4632-9869-18bce0a2117e">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: [],
	paging: {}
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</when>
			<otherwise>
				<ee:transform doc:name="payload" doc:id="31544d02-e30c-4767-b364-0d25d40d82ec">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
var data = read(payload as String,"application/JSON") as Object
var documents = data.Table as Array
var paging = (data.Table1 as Array)[0] as Object
output application/json
---
{
	data: documents map(item) -> {
		po_number: item.context_id,
		filename: item.filename,
		filesize: item.filesize,
		mimetype: item.mimetype,
		created_at: if ( isEmpty(item.created_at) ) "" else item.created_at as DateTime as String {
			format: "uuuu-MM-dd HH:mm:ss.SSS+00:00"
		},
		document_guid: upper(item.document_guid),
		document_type: item.document_type
	},
	paging: paging
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice>  
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="f9eea4b5-8783-465d-8e49-ab2742d2c83d" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "company_transaction_documents_agreements_get",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
	</flow>
	<flow name="company_transaction_documents_deliveries_get" doc:id="ab6f3dde-21cf-4d7c-9ec6-60009a928e44" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="b1d8e343-11fb-4f0e-8933-0876d47801fc" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "company_transaction_documents_deliveries_get",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		 <set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var transactionID = attributes.uriParams.transactionID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@ConfirmID=" ++ transactionID as String ++&#10;";@DocumentType=Deliveries" as String ++&#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="cbfa4303-f7bd-4f59-91b5-25693918bc13" variableName="parameters" /> 
		<!-- <http:request method="GET" doc:name="EXEC erp_tdp_Documents" doc:id="56251411-c400-4a20-9537-015c3fd442f3" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="NEVER" responseTimeout="60000">
				<reconnect />
				<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	"schema" : "erp",
	"objectType" : "StoredProcedure",
	"objectName" : "tdp_Documents",
	"parameters" : vars.parameters,
	"database" : "Artemis"
}]]]></http:query-params>
			</http:request> -->
			
		<http:request method="GET" doc:name="Call Artemis SYS api" doc:id="03021679-de87-4efd-b348-40bf3c30da08" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		 <choice doc:name="Choice" doc:id="f564bc0b-17f9-4a66-8257-dcb1461f4964" >
			<when expression='#[(sizeOf(payload.response.Table)) &lt; 1]' >
				<ee:transform doc:name="Transform Message" doc:id="649db7cf-31c7-44a3-adc6-0b517675a927" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	data: [],
	paging: {}
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</when>
			<otherwise >
				<ee:transform doc:name="payload" doc:id="40a3db9c-8cbf-4e10-8a62-fe6f2103f927" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
var data = read(payload as String,"application/JSON") as Object
var documents = data.Table as Array
var paging = (data.Table1 as Array)[0] as Object
output application/json
---
{
	data: documents map(item) -> {
		po_number: item.context_id,
		filename: item.filename,
		filesize: item.filesize,
		mimetype: item.mimetype,
		created_at: if ( isEmpty(item.created_at) ) "" else item.created_at as DateTime as String {
			format: "uuuu-MM-dd HH:mm:ss.SSS+00:00"
		},
		document_guid: upper(item.document_guid),
		document_type: item.document_type
	},
	paging: paging
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="62a27388-423e-498d-8f0d-467037841cba" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company-transactions-documents\company_transaction_documents_deliveries_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</flow>
	<flow name="company_transaction_documents_invoices_get" doc:id="fe65f867-ed04-4499-bb13-4e4e108b37e2" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="f6a76cea-907a-4e8a-8adf-f86c644c23bc" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "company_transaction_documents_invoices_get",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<set-variable value='#[%dw 2.0&#10;var companyID = attributes.uriParams.companyID default 0 as String&#10;var transactionID = attributes.uriParams.transactionID default 0 as String&#10;var pageSize = attributes.queryParams.pageSize default 1 as String&#10;var pageNumber = attributes.queryParams.pageNumber default 100 as String&#10;var paging = attributes.queryParams.paging default 0 as String&#10;output text/plain&#10;---&#10;"@CounterpartyID=" ++ companyID as String ++&#10;";@ConfirmID=" ++ transactionID as String ++&#10;";@DocumentType=Invoices" as String ++&#10;";@PageNumber=" ++ pageNumber as String ++&#10;";@PageSize=" ++ pageSize as String ++ &#10;";@Paging=" ++ paging as String&#93;' doc:name="parameters" doc:id="27883112-5975-47e3-b9fa-c5fcea37dacd" variableName="parameters" />
		<!-- <http:request method="GET" doc:name="EXEC erp_tdp_Documents" doc:id="a8bdb409-1369-46ea-8ddc-6c4a2b821fa8" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="NEVER" responseTimeout="60000">
				<reconnect />
				<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	"schema" : "erp",
	"objectType" : "StoredProcedure",
	"objectName" : "tdp_Documents",
	"parameters" : vars.parameters,
	"database" : "Artemis"
}]]]></http:query-params>
			</http:request> -->
			<http:request method="GET" doc:name="Call Artemis SYS api" doc:id="14dcb454-c641-4ef2-9d35-cf2467a95bbc" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.schema.documents'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.company.documents'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		<choice doc:name="Choice" doc:id="aedc43c2-882e-4936-ae45-2ec156fa3a44" >
			<when expression='#[(sizeOf(payload.response.Table)) &lt; 1]' >
				<ee:transform doc:name="Transform Message" doc:id="8f4bf982-a38e-4e9c-929a-eede75ec91db" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	data: [],
	paging: {}
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</when>
			<otherwise >
				<ee:transform doc:name="payload" doc:id="958b54a6-eaa5-4af5-b280-382394a8e7c5" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
var data = read(payload as String,"application/JSON") as Object
var documents = data.Table as Array
var paging = (data.Table1 as Array)[0] as Object
output application/json
---
{
	data: documents map(item) -> {
		po_number: item.context_id,
		filename: item.filename,
		filesize: item.filesize,
		mimetype: item.mimetype,
		created_at: if ( isEmpty(item.created_at) ) "" else item.created_at as DateTime as String {
			format: "uuuu-MM-dd HH:mm:ss.SSS+00:00"
		},
		document_guid: upper(item.document_guid),
		document_type: item.document_type
	},
	paging: paging
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice> 
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="c9d741ac-5a89-413a-a135-************" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "company_transaction_documents_invoices_get",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		 
	</flow>
	<flow name="companies-need-sync-lastsync" doc:id="4649601e-7d2f-4ca0-b067-1edcdb77ec61" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="f54b4d53-55f2-48f1-9e47-d30dddf8f153" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "company_transaction_documents_lastsync",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<set-variable value='#[%dw 2.0&#10;var lastSyncTime = vars.request.queryParameters.lastSync as String&#10;var pageSize = vars.request.queryParameters.pageSize default 1 as String&#10;var pageNumber = vars.request.queryParameters.pageNumber default 100 as String&#10;var paging = vars.request.queryParameters.paging default 0 as String&#10;output text/plain&#10;---&#10;"@lastSync=" ++ lastSyncTime as String]' doc:name="parameters" doc:id="0f118577-0528-4345-8239-6fecc1a666db" variableName="parameters" />
		<!-- <http:request method="GET" doc:name="EXEC tdp_companies_to_sync" doc:id="cdfeb995-cf0f-4785-9c6d-bf7d68bac77c" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="NEVER" responseTimeout="60000">
				<reconnect />
				<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	"schema" : "cp",
	"objectType" : "StoredProcedure",
	"objectName" : "tdp_companies_to_sync",
	"parameters" : vars.parameters,
	"database" : "Artemis"
}]]]></http:query-params>
			</http:request> -->
		<logger level="INFO" doc:name="Logger" doc:id="a1bb8a97-ac2a-4a50-b991-c0a7027cb36f" message="#[output application/java&#10;---&#10;{&#10;	database: Mule::p('cp.https.request.query.database'),&#10;	schema: Mule::p('cp.https.request.query.cp.schema'),&#10;	objectType: Mule::p('cp.https.request.query.objectType.sp'),&#10;	objectName: Mule::p('cp.https.request.query.objectName.documents.changedate'),&#10;	parameters: vars.parameters&#10;}]"/>
		<http:request method="GET" doc:name="Call Artemis SYS api" doc:id="e604df97-ee52-42da-bb77-71036e48ca88" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.companies.toSync'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		<logger level="INFO" doc:name="Logger" doc:id="6bcbac5a-77e2-440f-a698-82aba86894bc" message="#[payload]"/>
		<choice doc:name="Choice" doc:id="bd613db2-f191-45be-9eb2-624c10848f6b" >
			<when expression='#[((sizeOf(payload.response.Table) &gt; 0)) and (payload.response.Table[0].CounterpartyID != null)]' >
				<ee:transform doc:name="payload" doc:id="d17ad8f7-248d-4afe-8e54-c72d482093a0">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
CounterpartyIDs: payload.response.Table.CounterpartyID]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</when>
			<otherwise>
				<ee:transform doc:name="Transform Message" doc:id="6b8d1134-e9d9-44fb-b247-36bcafefeb8f">
					<ee:message>
						<ee:set-payload><![CDATA[%dw 2.0
 
output application/json
---

CounterpartyIDs: []]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice> 
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="aa9110c1-5e1a-4887-8ed5-51a4de0b4051" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "company_transaction_documents_lastsyncS",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		 
	</flow>
	<flow name="company_changeddocuments_lastsync" doc:id="985f457e-c6a3-489c-a8df-1bb692387854" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="12eaaa53-4904-4aab-9c1b-6fc0aebdbc98" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "company_transaction_documents_lastsync",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<set-variable value='#[%dw 2.0&#10;var lastSyncTime = vars.request.queryParameters.lastsync as String&#10;var pageSize = vars.request.queryParameters.pageSize default 1 as String&#10;var pageNumber = vars.request.queryParameters.pageNumber default 100 as String&#10;var paging = vars.request.queryParameters.paging default 0 as String&#10;output text/plain&#10;---&#10;"@LastSyncDate=" ++ lastSyncTime as String]' doc:name="parameters" doc:id="7fc1f079-dbd6-40f3-8ffb-8c4afa7996f8" variableName="parameters" />
		<!-- <http:request method="GET" doc:name="EXEC tdp_GET_Documents_ChangeDate" doc:id="7e1cd3ca-c617-4ca5-a7c0-773442028905" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="NEVER" responseTimeout="60000">
				<reconnect />
				<http:query-params><![CDATA[#[output application/java
-&#45;&#45;
{
	"schema" : "cp",
	"objectType" : "StoredProcedure",
	"objectName" : "tdp_GET_Documents_ChangeDate",
	"parameters" : vars.parameters,
	"database" : "Artemis"
}]]]></http:query-params>
			</http:request> -->
		<http:request method="OPTIONS" doc:name="Call Artemis SYS api" doc:id="059f70fd-e080-4f10-9cbd-966f376fb7c3" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure" sendBodyMode="NEVER">
			<error-mapping sourceType="HTTP:BAD_REQUEST" targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" />
			<http:query-params><![CDATA[#[output application/java
---
{
	database: Mule::p('cp.https.request.query.database'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	objectName: Mule::p('cp.https.request.query.objectName.documents.changedate'),
	parameters: vars.parameters
}]]]></http:query-params>
		</http:request>
		<choice doc:name="Choice" doc:id="9160128a-cfd4-4871-807d-01b083682a4c" >
			<when expression='#[payload contains("Timeout")]' >
				<ee:transform doc:name="Transform Message" doc:id="e258e657-5850-4e6d-89ab-b8aa155fe342" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</when>
			<otherwise >
				<ee:transform doc:name="payload" doc:id="2e911eeb-ff7f-4957-b2b0-5b94d0c6a3fe" >
					<ee:message >
						<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
var data = read(payload as String,"application/JSON") as Object
var documents = data.Table as Array
 
output application/json
---
{
	document: documents map(item) -> {
		ConfirmID: item.ConfirmID,
		AgreementID: item.AgreementID,
		CounterpartyID: item.CounterpartyID,
		DocumentName: item.DocumentName,
		DocumentSourceLocation: item.DocumentSourceLocation,
		DocumentTypeID: item.DocumentTypeID,
		
		LastModified: if ( isEmpty(item.LastModified) ) "" else item.LastModified as DateTime as String {
			format: "uuuu-MM-dd HH:mm:ss.SSS+00:00"
		}
		
	}	 
}]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice> 
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="4bd94924-f111-419c-be54-9c62e6ff7148" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "company_transaction_documents_lastsyncS",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		 
	</flow>
</mule>