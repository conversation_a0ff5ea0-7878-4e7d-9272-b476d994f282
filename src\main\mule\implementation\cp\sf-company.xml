<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:aggregators="http://www.mulesoft.org/schema/mule/aggregators" xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	xmlns:batch="http://www.mulesoft.org/schema/mule/batch"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/batch http://www.mulesoft.org/schema/mule/batch/current/mule-batch.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/aggregators http://www.mulesoft.org/schema/mule/aggregators/current/mule-aggregators.xsd">
<flow name="company_get" doc:id="523bdc00-3bcb-4988-a65a-c8fd5af6a2be">
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="bb9312c7-91ef-4f29-ba3e-5e8723369b5a" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-company\company_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]'/>
		<try doc:name="Try Executing Stored Procedure" doc:id="fcbe02ad-d41c-4bda-a0d9-1217a08bf1b9" >
			<http:request method="GET" doc:name="call Artemis SYS API" doc:id="5c96aa23-c83e-47f4-9d75-c3098d1a3aea" sendBodyMode="NEVER" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/stored-procedure">
				<error-mapping targetType="APP:ERPAPI_ARTEMIS_STOREDPROCEDURE" sourceType="HTTP:BAD_REQUEST" />
				<http:query-params><![CDATA[#[%dw 2.0
var companyName = "$(attributes.queryParams.name default '' as String)"
var companyId = "$(attributes.queryParams.id default '' as String)"
var pageSize = "$(attributes.queryParams.pageSize default '' as String)"
var pageNumber = "$(attributes.queryParams.pageNumber default '' as String)"
output application/json
---
{
	database: Mule::p('cp.https.request.query.database'),
	objectType: Mule::p('cp.https.request.query.objectType.sp'),
	schema: Mule::p('cp.https.request.query.cp.schema'),
	objectName: Mule::p('cp.https.request.query.objectName.companies'),
	parameters: "@CompanyID=$(companyId);@CompanyName=$(companyName);@PageSize=$(pageSize);@PageNumber=$(pageNumber)"
}]]]></http:query-params>
		</http:request>
		</try>
		<ee:transform doc:name="payload" doc:id="e837c613-ab7d-4994-8cde-bf0637ab7b2b">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	data: payload.response.Table as Array<Object>,
	paging: payload.response.Table1[0] as Object
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus"><![CDATA[%dw 2.0
output application/java
---
payload.statusCode as Number]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="f3c5ee54-1574-4575-a488-0a7250a1868f" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-company\company_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	
</flow>
	</mule>
