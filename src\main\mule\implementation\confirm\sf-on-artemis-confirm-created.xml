<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-on-artemis-confirm-created" doc:id="058d7f87-5b7a-424b-8c94-f5fa62ac5c1c" >
				<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="972c8e8e-0c44-4ad0-8143-cbe377ff3e30" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "sf-on-artemis-confirm-created", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey" : vars.businessKey&#10;	&#10;	}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="00bb69fb-236c-45db-b9f8-c4ae68a546b3" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "sf-on-artemis-confirm-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="4a44f68f-6865-42e6-9154-638a8e38c64b" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "sf-on-artemis-confirm-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<http:request method="POST" doc:name="Call OrderPrc to process record" doc:id="b50f9281-0437-422c-8f33-5bd5cb48da0a" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.confirms.path')]">
					<http:headers><![CDATA[#[output application/java
		---
		{
			"correlationId" : vars.vCorrelationId,
			"destinationId" : "order-prc-api",
			"x-source" : "artemis-exp-api",
			"x-msg-timestamp" : vars.vMsgTimestamp,
			"x-transactionId" : vars.vCorrelationId,
			"sourceId" : "artemis-exp-api",
			"x-businessKey": vars.businessKey
		}]]]></http:headers>
				</http:request>
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="341df78d-028d-493f-b78e-9faa57e11b65" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "sf-on-artemis-confirm-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="e29eb668-0b8c-416d-b160-c49316bc9292" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "sf-on-artemis-confirm-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="c27e8c4c-1217-41ef-a99e-25c30215ec2a" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "sf-on-artemis-confirm-created", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
	</sub-flow>
</mule>
