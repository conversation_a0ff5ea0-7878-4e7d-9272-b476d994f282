<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-on-artemis-bill-created" doc:id="b1224d13-af7a-416b-9067-ecf968db5351" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="f9788cea-5935-4642-992d-ac3ce142e103" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "sf-on-artemis-bill-created", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="77f2f4c3-d0c1-4ae8-bce6-0f5997f79527" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "sf-on-artemis-bill-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="553ca628-79ee-4aa6-87af-369c787ca6ee" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "sf-on-artemis-bill-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<http:request method="POST" doc:name="Call OrderPrc to process record" doc:id="2f60c4bb-daaf-4cb4-b292-412c361570ee" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.bills.path')]">
					<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "artemis-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vCorrelationId,
	"sourceId" : "artemis-exp-api",
	"x-businessKey": vars.businessKey
}]]]></http:headers>
				</http:request>
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="4a8c7c78-5e3d-4522-8a37-d651546ec8a0" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "sf-on-artemis-bill-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="3837141e-96c4-4621-8871-3c8411bd19f8" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "sf-on-artemis-bill-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="c8afe0c8-1077-4031-8490-77f06ad7b57a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "sf-on-artemis-bill-created", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />

	</sub-flow>
</mule>
