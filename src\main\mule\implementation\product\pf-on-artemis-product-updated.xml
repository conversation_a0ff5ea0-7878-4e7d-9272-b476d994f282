<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">

	<flow name="pf-on-artemis-product-updated"
		doc:id="912d69f4-c671-4ed1-b964-3c7ae63dcdba" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="9c6492a7-118b-407e-985f-7a3cd3e201d5"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-artemis-product-updated", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="277300a3-3268-433e-aa5e-bed5a0eaffae"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-artemis-product-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="1bb14737-c685-4a39-ad67-643f9b5c46db"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-artemis-product-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform
			doc:name="Set vProductId, vRequestAttributes"
			doc:id="734f016a-c1c9-4f20-b915-b63570a4d338">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": "ARTEMIS",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": (now() as LocalDateTime {format: "yyyy-MM-dd'T'HH:mm:ss.000'Z'"}),
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_EXP_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
				<ee:set-variable variableName="vProductId" ><![CDATA[attributes.queryParams.'productID']]></ee:set-variable>
			</ee:variables>
		</ee:transform>
    
		<choice doc:name="Check enableSyncForArtemis" doc:id="571cf51d-39f3-41a8-876e-face3cbe1e42" >
			<when expression="#[Mule::p('enableSyncForArtemis') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is enabled" doc:id="1af421a8-f1c7-4f7a-a930-a3081cb25386" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForArtemis is enabled", &#10;	"FlowName" : "pf-on-artemis-product-updated", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<http:request method="GET" doc:name="Fetch ENTERPRISE_ID from REF_ID" doc:id="667559a5-0cf0-4963-b88c-067703a8a26e" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.refId.path')]" target="vRefIdResponse">
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
			<http:query-params><![CDATA[#[output application/java
---
{
	"ARTEMIS_ID": vars.vproductId,
	"OBJECT_TYPE": "PRODUCT"
}]]]></http:query-params>
		</http:request>
				<choice doc:name="Check if record exists in REF_ID" doc:id="6b2f6404-6cd6-4aef-b639-f7e70cdc02f5">
					<when expression="#[!isEmpty(vars.vRefIdResponse.'response')]">
						<ee:transform doc:name="Set vSyncableFieldUpdateFlag" doc:id="47d402a9-1330-44bb-a642-89e90450628e">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vSyncableFieldUpdateFlag"><![CDATA[%dw 2.0
var vRefIdRecord = if(isEmpty(vars.vRefIdResponse.response[0].'LAST_PAYLOAD_ARTEMIS')) null else (read(vars.vRefIdResponse.response[0].'LAST_PAYLOAD_ARTEMIS' default "", "application/json")).'product'
var vArtemisRecord = payload.'product'
output application/json
--- 
if(vRefIdRecord ~= null)
	true
else if(
	(vRefIdRecord.'productID' ~= vArtemisRecord.'productID') and
	// (vRefIdRecord.'changeUser' ~= vArtemisRecord.'changeUser') and
	(vRefIdRecord.'activeForCommercial' ~= vArtemisRecord.'activeForCommercial') and
	(vRefIdRecord.'retailName' ~= vArtemisRecord.'retailName')
	
)
	false
else 
	true]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
						<choice doc:name="Check if sync is required" doc:id="46348509-d021-4ea1-b035-d1635f6c51fa">
			<when expression="#[((vars.vSyncableFieldUpdateFlag))]">
				<set-payload value='#[output application/json&#10;---&#10;{&#10;    product: payload.product mapObject(v, k, i) -&gt; {&#10;    ((k) match {&#10;        case "productID" -&gt; "productId"&#10;        else -&gt; (k)&#10;    }): (v)&#10;}&#10;}]' doc:name="Set Payload" doc:id="45343dee-63dc-49df-b26b-797616b5df04" />
								<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="00f45ab0-384d-483e-9c73-9c59fbbab9a4">
					<ee:message />
					<ee:variables>
						<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "UPDATE",
    "SOURCE": "ARTEMIS",
    "STATUS": "QUEUED",
    "LAST_UPDATED_BY": "EXPERIENCE_API",
    "ENTERPRISE_ID":  vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default null, 
    "PAYLOAD": write(payload, 'application/json') default null,
    "OBJECT_TYPE": "PRODUCT",
    "RETRY_COUNT": 0,
    //"PRIORITY":(payload.product.syncPriority default "0") as Number,
    "QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
    "ERROR_MSG": null,
    "ERROR_TYPE": null,
    "RECORD_ID": vars.vAttributes.queryParams.productID as String
  }
}]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAIL" doc:id="5bae48ca-48f0-471a-bf4f-0df561fb19b0" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionDetails.path')]" target="vInsertTransactionResponse">
					<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
					<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
				</http:request>
								<http:request method="GET" doc:name="Call SyncPrc to process products" doc:id="5fbd5ca2-1eac-4d05-b36f-9146f169445d" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncProducts.path')]" target="vSyncRecordResponse" >
									<http:body ><![CDATA[#[{}]]]></http:body>
									<http:headers ><![CDATA[#[output application/java
---
{
	correlationId : vars.vCorrelationId
}]]]></http:headers>
								</http:request>
								<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="d5d4dc56-05e5-40c3-a363-6f81b1dfd603" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-on-artemis-product-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
								<ee:transform doc:name="Set payload, httpStatus" doc:id="8357de16-36d6-4c1a-8a2d-7835e030ea25">
									<ee:message>
										<ee:set-payload><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
	"eid": vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default ""
  }
}]]></ee:set-payload>
									</ee:message>
									<ee:variables>
										<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
									</ee:variables>
								</ee:transform>
			</when>
			<otherwise>
				<logger level="INFO" doc:name="LOG INFO: Non Syncable field updated" doc:id="156f319b-e7aa-4f39-ad84-391581f39520" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "A non-syncable field has been updated. NO Sync is required for this product",&#10;	"FlowName": "pf-on-artemis-product-modified",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
								<ee:transform doc:name="Set payload, httpStatus" doc:id="f1191332-1ed3-4c3c-a337-e7d9885cdb91" >
									<ee:message >
										<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Product will not be processed for syncing",
  	"description": "Non syncable field has been updated."
  }
}]]></ee:set-payload>
									</ee:message>
									<ee:variables >
										<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
									</ee:variables>
								</ee:transform>
			</otherwise>
		</choice>
					</when>
					<otherwise >
						<logger level="INFO" doc:name="LOG INFO: Record does not exist in REF_ID" doc:id="0f5e7df9-d1ad-46db-b449-1f27cb92047c" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Record does not exist in REF_ID table",&#10;	"FlowName" : "pf-on-artemis-product-updated",&#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
						<ee:transform doc:name="Set payload, httpStatus" doc:id="bde40758-1eef-4a7a-8932-737ed92a9e72" >
							<ee:message >
								<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Record does not exist in REF_ID table"
  }
}]]></ee:set-payload>
							</ee:message>
							<ee:variables >
								<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
					</otherwise>
				</choice>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is disabled" doc:id="cb61b4de-a40a-4dfd-9b44-938d8e60af38" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForArtemis is disabled", &#10;	"FlowName" : "pf-on-artemis-product-updated", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="15722399-6be8-4abb-b786-c89d12168fae" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForArtemis is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="d1bdc809-5e0e-488a-bd83-1676aaa191f8"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-artemis-product-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="ca5604f1-03e3-4134-b161-a1160d5bb8cc"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-artemis-product-updated",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="9dc9dae5-31c7-49de-bccd-953c3d0f8570"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-artemis-product-updated",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
