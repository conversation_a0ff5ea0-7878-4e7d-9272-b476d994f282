<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-on-artemis-confirm-updated" doc:id="6d01f0e2-20a4-485a-9f8b-3af996204f51" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="b4456688-d81a-42a8-a38a-c98a437cb9c7" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "sf-on-artemis-confirm-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="cd5773e5-f273-47df-839d-5c5b18e21167" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "sf-on-artemis-confirm-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="79b42f84-1ed3-4f3c-83a6-f835ef8edeaa" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "sf-on-artemis-confirm-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<http:request method="PUT" doc:name="Call OrderPrc to process record" doc:id="f5f63736-66d7-472b-b655-2b6e2676dfce" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.confirms.path')]">
					<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "artemis-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vCorrelationId,
	"sourceId" : "artemis-exp-api",
	"x-businessKey": vars.businessKey
}]]]></http:headers>
				</http:request>
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="80ad85dd-b675-4549-a6cc-b02461075249" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "sf-on-artemis-confirm-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="6c6d181c-1464-4b0e-baa7-8c4ff1d59f05" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "sf-on-artemis-confirm-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="27828242-6442-412d-9331-21f0d1930e49" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "sf-on-artemis-confirm-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />

	</sub-flow>
</mule>
