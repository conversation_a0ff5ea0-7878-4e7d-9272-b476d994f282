<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="document_get" doc:id="0d0dfac7-6b55-4545-985d-8b1fd214099b" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="897cdc4b-2a3f-4726-a448-68a48d395d73" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-documents\document_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Document" doc:id="43b6950e-4e9c-4e09-9dd2-302937313cf2" >
			<http:request method="GET" doc:name="ERP Artemis API Document" doc:id="3663d0c3-da10-48b2-a372-2d75c4e27ff9" config-ref="HTTPS_Request_Artemis_SYS_API" path="/api/artemis/document" sendBodyMode="NEVER">
				<http:query-params><![CDATA[#[output application/java
---
{
	documentID : attributes.uriParams.documentID
}]]]></http:query-params>
			</http:request>
		</try>
		<ee:transform doc:name="payload" doc:id="f92fbd15-8173-4f17-a43c-6b1be994ccf2">
						<ee:message>
							<ee:set-payload><![CDATA[payload]]></ee:set-payload>
						</ee:message>
					</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="8ef807ea-f2b5-430b-86ce-afb855b1a456" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-documents\document_get",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
</mule>
