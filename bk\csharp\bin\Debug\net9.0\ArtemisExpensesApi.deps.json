{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"ArtemisExpensesApi/1.0.0": {"dependencies": {"Microsoft.AspNetCore.OpenApi": "9.0.7", "Swashbuckle.AspNetCore": "9.0.4"}, "runtime": {"ArtemisExpensesApi.dll": {}}}, "Microsoft.AspNetCore.OpenApi/9.0.7": {"dependencies": {"Microsoft.OpenApi": "1.6.25"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {}, "Microsoft.OpenApi/1.6.25": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/9.0.4": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.4", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.4", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.4"}}, "Swashbuckle.AspNetCore.Swagger/9.0.4": {"dependencies": {"Microsoft.OpenApi": "1.6.25"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.4.1727"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.4": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.4"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.4.1727"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.4": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.4.1727"}}}}}, "libraries": {"ArtemisExpensesApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OpenApi/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-8aG0mkgmA38IDJ0ca5HIpdexKjHXIh0z1kIdw5WyM6CrD4+CEt97UgSwBBBCHG6QQKV0hj2mfkwtEcqrJBcu8g==", "path": "microsoft.aspnetcore.openapi/9.0.7", "hashPath": "microsoft.aspnetcore.openapi.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Kzzf7pRey40VaUkHN9/uWxrKVkLu2AQjt+GVeeKLLpiEHAJ1xZRsLSh4ZZYEnyS7Kt2OBOPmsXNdU+wbcOl5w==", "path": "microsoft.extensions.apidescription.server/9.0.0", "hashPath": "microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.25": {"type": "package", "serviceable": true, "sha512": "sha512-ZahSqNGtNV7N0JBYS/IYXPkLVexL/AZFxo6pqxv6A7Uli7Q7zfitNjkaqIcsV73Ukzxi4IlJdyDgcQiMXiH8cw==", "path": "microsoft.openapi/1.6.25", "hashPath": "microsoft.openapi.1.6.25.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4hghaogMoS87Ivjj8s7aGuGsxrsjXZpjNvahLsN+zSLrZQcV8zQyiBweBd9AXC1sGkeNYb9/hbeS1EXrZ/hKjQ==", "path": "swashbuckle.aspnetcore/9.0.4", "hashPath": "swashbuckle.aspnetcore.9.0.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-syU8U4Eg3DfhU+BBeDUh66erwDsYOhp82InXbrOsqWP3qoOfQbBtePcTetkLNanovYHYX40alZBE6gQQFtBZkQ==", "path": "swashbuckle.aspnetcore.swagger/9.0.4", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-GnCikaq7kagEckGGsrVnKl2icRQebyr14/7s3T/rQQO7edOIXkxtjTOJZqbazOxaTXBDCDdSInMiYbMQhFnE5Q==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.4", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-2ugT2OvZsKHqk/2rMDmuqDuFmtix0NvzlXxAfnfHROVMTovbx7Z0UsOQHZa462DBTgdBFnR2Ss6wm4fypfymdA==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.4", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.4.nupkg.sha512"}}}