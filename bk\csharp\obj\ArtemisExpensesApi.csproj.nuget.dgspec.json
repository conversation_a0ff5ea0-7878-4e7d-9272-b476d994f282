{"format": 1, "restore": {"C:\\Prasad\\Technical\\apps\\murali\\Apisero_Artemis_EXP_API-main\\csharp\\ArtemisExpensesApi.csproj": {}}, "projects": {"C:\\Prasad\\Technical\\apps\\murali\\Apisero_Artemis_EXP_API-main\\csharp\\ArtemisExpensesApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Prasad\\Technical\\apps\\murali\\Apisero_Artemis_EXP_API-main\\csharp\\ArtemisExpensesApi.csproj", "projectName": "ArtemisExpensesApi", "projectPath": "C:\\Prasad\\Technical\\apps\\murali\\Apisero_Artemis_EXP_API-main\\csharp\\ArtemisExpensesApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Prasad\\Technical\\apps\\murali\\Apisero_Artemis_EXP_API-main\\csharp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.7, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}