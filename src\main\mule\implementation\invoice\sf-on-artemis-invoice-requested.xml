<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="sf-on-artemis-invoice-requested" doc:id="a7bc97ce-d98e-4c1e-aea9-d32160458a35" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="03b82438-6ed6-4f42-adc5-af38e8680989" message='#[%dw 2.0&#10;output application/json &#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "sf-on-artemis-invoice-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="a4508b92-95eb-48b8-83db-b498c6e9ece9" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "sf-on-artemis-invoice-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="96a05486-bb21-46c2-a15b-654094801be4" message='#[%dw 2.0&#10;output application/json writeAttributes=true&#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "sf-on-artemis-invoice-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<http:request method="PUT" doc:name="Call OrderPrc to process record" doc:id="506b9756-22e7-410a-a687-9d49ba263dc9" config-ref="HTTPS_Request_Order_Prc_API" path="#[p('https.request.orderPrcApi.billingSchedules.path')]">
					<http:headers><![CDATA[#[output application/java
---
{
	"correlationId" : vars.vCorrelationId,
	"destinationId" : "order-prc-api",
	"x-source" : "artemis-exp-api",
	"x-msg-timestamp" : vars.vMsgTimestamp,
	"x-transactionId" : vars.vCorrelationId,
	"sourceId" : "artemis-exp-api",
	"x-businessKey": vars.businessKey
}]]]></http:headers>
				</http:request>
				<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="774a260f-f587-4e9b-83d8-20f992e1c35a" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response Payload",&#10;	"FlowName" : "sf-on-artemis-invoice-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="c8a4113d-9bab-4669-9fad-7a2eff0f83b2" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "sf-on-artemis-invoice-updated",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/confirms",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="89b9b031-3cf0-474b-9326-f73883fbaf28" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Ended", &#10;	"FlowName" : "sf-on-artemis-invoice-updated", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />

	</sub-flow>
</mule>
